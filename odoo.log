2025-05-25 18:53:49,727 29129 DEBUG ? odoo.http: HTTP sessions stored in: /root/.local/share/Odoo/sessions 
2025-05-25 18:53:49,732 29129 DEBUG ? odoo.modules.registry: Multiprocess load registry signaling: [Registry: 67] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:53:49,733 29129 INFO ? odoo.modules.loading: loading 1 modules... 
2025-05-25 18:53:49,733 29129 DEBUG ? odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:53:49,736 29129 DEBUG ? odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,736 29129 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:53:49,743 29129 DEBUG ? odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:53:49,744 29129 INFO ? odoo.modules.loading: loading 161 modules... 
2025-05-25 18:53:49,744 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:53:49,744 29129 DEBUG ? odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,744 29129 DEBUG ? odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:53:49,744 29129 DEBUG ? odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,744 29129 DEBUG ? odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:53:49,745 29129 DEBUG ? odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,745 29129 DEBUG ? odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:53:49,745 29129 DEBUG ? odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,745 29129 DEBUG ? odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:53:49,746 29129 DEBUG ? odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,746 29129 DEBUG ? odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:53:49,746 29129 DEBUG ? odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,746 29129 DEBUG ? odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:53:49,746 29129 DEBUG ? odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,747 29129 DEBUG ? odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:53:49,748 29129 DEBUG ? odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,748 29129 DEBUG ? odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:53:49,748 29129 DEBUG ? odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,748 29129 DEBUG ? odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:53:49,749 29129 DEBUG ? odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,749 29129 DEBUG ? odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:53:49,749 29129 DEBUG ? odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,749 29129 DEBUG ? odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:53:49,750 29129 DEBUG ? odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,750 29129 DEBUG ? odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,751 29129 DEBUG ? odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:53:49,752 29129 DEBUG ? odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,752 29129 DEBUG ? odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:53:49,754 29129 DEBUG ? odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,754 29129 DEBUG ? odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:53:49,756 29129 DEBUG ? odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,756 29129 DEBUG ? odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:53:49,756 29129 DEBUG ? odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,757 29129 DEBUG ? odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,758 29129 DEBUG ? odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:53:49,759 29129 DEBUG ? odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,759 29129 DEBUG ? odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:53:49,759 29129 DEBUG ? odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,760 29129 DEBUG ? odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:53:49,760 29129 DEBUG ? odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,760 29129 DEBUG ? odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,761 29129 DEBUG ? odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,762 29129 DEBUG ? odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,764 29129 DEBUG ? odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,765 29129 DEBUG ? odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:53:49,767 29129 DEBUG ? odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,767 29129 DEBUG ? odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:53:49,767 29129 DEBUG ? odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,767 29129 DEBUG ? odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,770 29129 DEBUG ? odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:53:49,772 29129 DEBUG ? odoo.modules.loading: Module stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,772 29129 DEBUG ? odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,773 29129 DEBUG ? odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:53:49,774 29129 DEBUG ? odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,774 29129 DEBUG ? odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:53:49,774 29129 DEBUG ? odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,774 29129 DEBUG ? odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,775 29129 DEBUG ? odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,776 29129 DEBUG ? odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,777 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:53:49,778 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,778 29129 DEBUG ? odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:53:49,778 29129 DEBUG ? odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,778 29129 DEBUG ? odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,779 29129 DEBUG ? odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,780 29129 DEBUG ? odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:53:49,781 29129 DEBUG ? odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,781 29129 DEBUG ? odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:53:49,781 29129 DEBUG ? odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,782 29129 DEBUG ? odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,784 29129 DEBUG ? odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:53:49,785 29129 DEBUG ? odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,786 29129 DEBUG ? odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:53:49,788 29129 DEBUG ? odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,788 29129 DEBUG ? odoo.modules.loading: Loading module bs_pos_mcurrancy (107/161) 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Module bs_pos_mcurrancy loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Loading module hr_expense_extract (108/161) 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Loading module pos_avatax (109/161) 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Loading module pos_barcodelookup (110/161) 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,789 29129 DEBUG ? odoo.modules.loading: Loading module pos_enterprise (111/161) 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Loading module pos_epson_printer (112/161) 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Loading module pos_hr (113/161) 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,790 29129 DEBUG ? odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,791 29129 DEBUG ? odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,792 29129 DEBUG ? odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,793 29129 DEBUG ? odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,794 29129 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,795 29129 DEBUG ? odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 18:53:49,796 29129 DEBUG ? odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,796 29129 DEBUG ? odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 18:53:49,796 29129 DEBUG ? odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,796 29129 DEBUG ? odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 18:53:49,797 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,798 29129 DEBUG ? odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,799 29129 DEBUG ? odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 18:53:49,800 29129 DEBUG ? odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:53:49,800 29129 INFO ? odoo.modules.loading: 161 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-05-25 18:53:49,975 29129 INFO ? odoo.modules.loading: Modules loaded. 
2025-05-25 18:53:49,978 29129 INFO ? odoo.modules.registry: Registry loaded in 0.250s 
2025-05-25 18:53:49,979 29129 INFO 1005 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-05-25 18:53:50,001 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET / HTTP/1.1" 303 - 25 0.011 0.263
2025-05-25 18:53:50,217 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /odoo HTTP/1.1" 200 - 106 0.027 0.187
2025-05-25 18:53:50,235 29129 DEBUG ? odoo.modules.module: module __init__.py: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,236 29129 DEBUG ? odoo.modules.module: module __pycache__: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,320 29129 DEBUG ? odoo.modules.module: module website_twitter: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,332 29129 DEBUG ? odoo.modules.module: module LICENSE: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,364 29129 DEBUG ? odoo.modules.module: module CONTRIBUTING.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,372 29129 DEBUG ? odoo.modules.module: module README.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,417 29129 DEBUG ? odoo.modules.module: module COPYRIGHT: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:50,450 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /web/assets/24e783d/web.assets_web.min.css HTTP/1.1" 200 - 3 0.001 0.216
2025-05-25 18:53:50,547 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /web/webclient/translations/4e27287b6303aecff64d62f564a779a603cd7f80?lang=en_US HTTP/1.1" 200 - 1 0.001 0.006
2025-05-25 18:53:50,549 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /web/assets/429f6f4/web.assets_web.min.js HTTP/1.1" 200 - 3 0.002 0.006
2025-05-25 18:53:50,640 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /web/webclient/load_menus/31bfdde14b3ebc408d37ef2c867a2bab5d27b6680c50c3bfc270fc22dd3a691a HTTP/1.1" 200 - 256 0.043 0.057
2025-05-25 18:53:50,763 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:50] "GET /web/assets/9993b8e/web.assets_web_print.min.css HTTP/1.1" 200 - 3 0.001 0.003
2025-05-25 18:53:51,021 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:51,059 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "POST /mail/data HTTP/1.1" 200 - 37 0.010 0.013
2025-05-25 18:53:51,063 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web_enterprise/static/img/background-light.svg HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:51,078 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 16 0.006 0.009
2025-05-25 18:53:51,089 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 200 - 4 0.003 0.005
2025-05-25 18:53:51,097 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/image/res.partner/3/avatar_128?unique=1748022128000 HTTP/1.1" 200 - 8 0.006 0.010
2025-05-25 18:53:51,325 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:51,397 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.002
2025-05-25 18:53:51,403 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.003
2025-05-25 18:53:51,410 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.004 0.006
2025-05-25 18:53:51,712 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:51,715 29129 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-05-25 18:53:51,721 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:51] "GET /odoo/offline HTTP/1.1" 200 - 6 0.003 0.010
2025-05-25 18:53:54,098 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.006
2025-05-25 18:53:54,431 29129 DEBUG 1005 odoo.api: call ir.module.module().get_views(options={'action_id': 39, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'kanban'], [False, 'list'], [False, 'form'], [95, 'search']]) 
2025-05-25 18:53:54,543 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.013 0.102
2025-05-25 18:53:54,691 29129 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('module_type', category_domain=[], enable_counters=False, expand=True, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:53:54,691 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:53:54,863 29129 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:53:54,864 29129 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=[['application', '=', True]], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:53:54,873 29129 DEBUG 1005 odoo.modules.module: module wbl_pos_multi_currency: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:53:54,885 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.003 0.021
2025-05-25 18:53:54,889 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 53 0.017 0.012
2025-05-25 18:53:54,987 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:54] "GET /sale_management/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:55,291 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:55,293 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /project/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:55,297 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /documents/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-05-25 18:53:55,298 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /point_of_sale/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-05-25 18:53:55,299 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /hr_holidays/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-05-25 18:53:55,300 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /hr_recruitment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-05-25 18:53:55,597 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /hr_expense/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:55,603 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /web_studio/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:55,608 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /timesheet_grid/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-05-25 18:53:55,610 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /purchase/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:55,611 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /accountant/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-05-25 18:53:55,611 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /mrp/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-05-25 18:53:55,904 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /mass_mailing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:55,907 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /website_sale/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:55,910 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /hr/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:55,916 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /delivery_envia/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:55,916 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /data_recycle/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:55,917 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:55] "GET /delivery_fedex_rest/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,213 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /delivery_ups_rest/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:56,213 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /delivery_starshipit/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,216 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /frontdesk/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,220 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /knowledge/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:56,223 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /marketing_card/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,224 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /maintenance/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,521 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /os_pos_multi_currency/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:56,521 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /room/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:56,527 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /pos_restaurant/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-05-25 18:53:56,530 29129 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:53:56,531 29129 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=[]) 
2025-05-25 18:53:56,532 29129 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:53:56,574 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 51 0.036 0.013
2025-05-25 18:53:56,585 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.004 0.053
2025-05-25 18:53:56,637 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 63 0.020 0.098
2025-05-25 18:53:56,841 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /account/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:56,843 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /web/static/img/placeholder.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,844 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:56,887 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /website/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:56,889 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /stock/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:56,950 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:56] "GET /whatsapp/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:57,150 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /sale_subscription/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,150 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /helpdesk/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-05-25 18:53:57,150 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /sign/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:57,196 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /website_slides/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:57,196 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /quality_control/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:57,263 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /planning/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:57,456 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /website_event/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,460 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /contacts/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-05-25 18:53:57,461 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /mail/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:57,506 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /mrp_plm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:57,507 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /sale_renting/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,567 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /calendar/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:57,770 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /industry_fsm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,771 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /social/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,772 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /hr_appraisal/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:57,815 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /approvals/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,816 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /fleet/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:57,877 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:57] "GET /marketing_automation/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,080 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /im_livechat/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,082 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /survey/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,084 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /appointment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,124 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /hr_referral/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,125 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /repair/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:58,185 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /hr_attendance/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,388 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /mass_mailing_sms/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,392 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /stock_barcode/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:58,392 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /iot/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:58,434 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /hr_skills/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,436 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /project_todo/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,495 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /voip/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,696 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /delivery_dhl/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,701 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /hr_payroll/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:58,701 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /delivery_fedex/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,743 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /delivery_usps/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:58,743 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /lunch/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:58,806 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:58] "GET /delivery_usps_rest/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:59,007 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /website_hr_recruitment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:59,010 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /delivery_easypost/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:59,011 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /delivery_sendcloud/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:59,053 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /delivery_shiprocket/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:59,055 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /sale_amazon/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:59,116 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /sale_shopee/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:59,319 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /delivery_bpost/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:59,320 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /hr_contract/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-05-25 18:53:59,324 29129 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=['|', '|', ['summary', 'ilike', 'multi'], ['shortdesc', 'ilike', 'multi'], ['name', 'ilike', 'multi']]) 
2025-05-25 18:53:59,352 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 51 0.020 0.013
2025-05-25 18:53:59,425 29129 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=['|', '|', ['summary', 'ilike', 'multi'], ['shortdesc', 'ilike', 'multi'], ['name', 'ilike', 'multi']], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:53:59,428 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.002 0.003
2025-05-25 18:53:59,670 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /base/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 18:53:59,747 29129 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /data_cleaning/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-25 18:53:59,749 29129 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:53:59,754 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:53:59] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 8 0.002 0.007
2025-05-25 18:54:02,531 29129 DEBUG 1005 odoo.api: call ir.module.module(1299,).button_immediate_upgrade() 
2025-05-25 18:54:02,532 29129 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:54:02,533 29129 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:54:02,533 29129 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:54:03,341 29129 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-05-25 18:54:03,362 29129 DEBUG 1005 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 67] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:54:03,364 29129 INFO 1005 odoo.modules.loading: loading 1 modules... 
2025-05-25 18:54:03,364 29129 DEBUG 1005 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:54:03,367 29129 DEBUG 1005 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:54:03,367 29129 INFO 1005 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:54:03,376 29129 INFO 1005 odoo.modules.loading: updating modules list 
2025-05-25 18:54:03,377 29129 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-05-25 18:54:04,012 29129 DEBUG 1005 odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:54:04,012 29129 INFO 1005 odoo.modules.loading: loading 161 modules... 
2025-05-25 18:54:04,012 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:54:04,012 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,012 29129 DEBUG 1005 odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:54:04,013 29129 DEBUG 1005 odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,013 29129 DEBUG 1005 odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,014 29129 DEBUG 1005 odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,015 29129 DEBUG 1005 odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,016 29129 DEBUG 1005 odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:54:04,017 29129 DEBUG 1005 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,017 29129 DEBUG 1005 odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:54:04,018 29129 DEBUG 1005 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,018 29129 DEBUG 1005 odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:54:04,018 29129 DEBUG 1005 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,018 29129 DEBUG 1005 odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:54:04,019 29129 DEBUG 1005 odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,019 29129 DEBUG 1005 odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:54:04,019 29129 DEBUG 1005 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,019 29129 DEBUG 1005 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,020 29129 DEBUG 1005 odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:54:04,021 29129 DEBUG 1005 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,021 29129 DEBUG 1005 odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:54:04,023 29129 DEBUG 1005 odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,023 29129 DEBUG 1005 odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:54:04,025 29129 DEBUG 1005 odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,025 29129 DEBUG 1005 odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,026 29129 DEBUG 1005 odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:54:04,027 29129 DEBUG 1005 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,028 29129 DEBUG 1005 odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:54:04,029 29129 DEBUG 1005 odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,029 29129 DEBUG 1005 odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:54:04,029 29129 DEBUG 1005 odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,029 29129 DEBUG 1005 odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:54:04,030 29129 DEBUG 1005 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,031 29129 DEBUG 1005 odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:54:04,032 29129 DEBUG 1005 odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,032 29129 DEBUG 1005 odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:54:04,033 29129 DEBUG 1005 odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,033 29129 DEBUG 1005 odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,034 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:54:04,035 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,035 29129 DEBUG 1005 odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:54:04,036 29129 DEBUG 1005 odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,036 29129 DEBUG 1005 odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:54:04,036 29129 DEBUG 1005 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,037 29129 DEBUG 1005 odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:54:04,039 29129 DEBUG 1005 odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,039 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:54:04,039 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,039 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:54:04,040 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,040 29129 DEBUG 1005 odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:54:04,041 29129 DEBUG 1005 odoo.modules.loading: Module stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,041 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:54:04,042 29129 DEBUG 1005 odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,043 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,044 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:54:04,045 29129 DEBUG 1005 odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,045 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:54:04,045 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,045 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:54:04,045 29129 DEBUG 1005 odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,046 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:54:04,047 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,047 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:54:04,047 29129 DEBUG 1005 odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,047 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,048 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,049 29129 DEBUG 1005 odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,050 29129 DEBUG 1005 odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,051 29129 DEBUG 1005 odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,053 29129 DEBUG 1005 odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,054 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,055 29129 DEBUG 1005 odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:54:04,056 29129 DEBUG 1005 odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,056 29129 INFO 1005 odoo.modules.loading: Loading module bs_pos_mcurrancy (107/161) 
2025-05-25 18:54:04,096 29129 DEBUG 1005 odoo.models: Patching res.partner.bank.partner_country_name with translate=True 
2025-05-25 18:54:04,096 29129 DEBUG 1005 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 18:54:04,097 29129 DEBUG 1005 odoo.models: Patching res.config.settings.invoice_terms with translate=True 
2025-05-25 18:54:04,097 29129 DEBUG 1005 odoo.models: Patching res.config.settings.invoice_terms_html with translate=True 
2025-05-25 18:54:04,101 29129 DEBUG 1005 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 18:54:04,101 29129 DEBUG 1005 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 18:54:04,101 29129 DEBUG 1005 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 18:54:04,102 29129 DEBUG 1005 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 18:54:04,106 29129 DEBUG 1005 odoo.models: Patching product.template.uom_name with translate=True 
2025-05-25 18:54:04,108 29129 DEBUG 1005 odoo.models: Patching product.pricelist.item.product_uom with translate=True 
2025-05-25 18:54:04,108 29129 DEBUG 1005 odoo.models: Patching product.template.attribute.value.name with translate=True 
2025-05-25 18:54:04,112 29129 DEBUG 1005 odoo.models: Patching account.move.line.account_name with translate=True 
2025-05-25 18:54:04,116 29129 DEBUG 1005 odoo.models: Patching account.report.expression.report_line_name with translate=True 
2025-05-25 18:54:04,118 29129 DEBUG 1005 odoo.models: Patching spreadsheet.dashboard.share.name with translate=True 
2025-05-25 18:54:04,121 29129 DEBUG 1005 odoo.models: Patching stock.replenishment.option.uom with translate=True 
2025-05-25 18:54:04,124 29129 DEBUG 1005 odoo.models: Patching stock.valuation.layer.revaluation.product_uom_name with translate=True 
2025-05-25 18:54:04,161 29129 DEBUG 1005 odoo.models: Patching res.partner.bank.partner_country_name with translate=True 
2025-05-25 18:54:04,161 29129 DEBUG 1005 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 18:54:04,162 29129 DEBUG 1005 odoo.models: Patching res.config.settings.invoice_terms with translate=True 
2025-05-25 18:54:04,162 29129 DEBUG 1005 odoo.models: Patching res.config.settings.invoice_terms_html with translate=True 
2025-05-25 18:54:04,166 29129 DEBUG 1005 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 18:54:04,166 29129 DEBUG 1005 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 18:54:04,166 29129 DEBUG 1005 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 18:54:04,167 29129 DEBUG 1005 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 18:54:04,173 29129 DEBUG 1005 odoo.models: Patching product.template.uom_name with translate=True 
2025-05-25 18:54:04,175 29129 DEBUG 1005 odoo.models: Patching product.pricelist.item.product_uom with translate=True 
2025-05-25 18:54:04,175 29129 DEBUG 1005 odoo.models: Patching product.template.attribute.value.name with translate=True 
2025-05-25 18:54:04,180 29129 DEBUG 1005 odoo.models: Patching account.move.line.account_name with translate=True 
2025-05-25 18:54:04,183 29129 DEBUG 1005 odoo.models: Patching account.report.expression.report_line_name with translate=True 
2025-05-25 18:54:04,186 29129 DEBUG 1005 odoo.models: Patching spreadsheet.dashboard.share.name with translate=True 
2025-05-25 18:54:04,190 29129 DEBUG 1005 odoo.models: Patching stock.replenishment.option.uom with translate=True 
2025-05-25 18:54:04,193 29129 DEBUG 1005 odoo.models: Patching stock.valuation.layer.revaluation.product_uom_name with translate=True 
2025-05-25 18:54:04,224 29129 INFO 1005 odoo.modules.registry: module bs_pos_mcurrancy: creating or updating database tables 
2025-05-25 18:54:04,225 29129 DEBUG 1005 odoo.schema: Table 'pos_config': column 'self_ordering_mode': dropped constraint NOT NULL 
2025-05-25 18:54:04,225 29129 DEBUG 1005 odoo.schema: Table 'pos_config': column 'self_ordering_service_mode': dropped constraint NOT NULL 
2025-05-25 18:54:04,226 29129 DEBUG 1005 odoo.schema: Table 'pos_config': column 'self_ordering_pay_after': dropped constraint NOT NULL 
2025-05-25 18:54:04,236 29129 DEBUG 1005 odoo.schema: Table 'pos_payment_method': added column 'currency_id' of type int4 
2025-05-25 18:54:04,237 29129 DEBUG 1005 odoo.schema: Table 'pos_payment_method': added column 'use_specific_currency' of type bool 
2025-05-25 18:54:04,238 29129 DEBUG 1005 odoo.schema: Table 'res_config_settings': column 'default_picking_policy': dropped constraint NOT NULL 
2025-05-25 18:54:04,241 29129 WARNING 1005 odoo.addons.base.models.ir_model: Two fields (rate_of_currency, currency_rate) of pos.payment() have the same label: Conversion Rate. [Modules: bs_pos_mcurrancy and point_of_sale] 
2025-05-25 18:54:04,241 29129 WARNING 1005 odoo.addons.base.models.ir_model: Two fields (symbol_of_currency, currency_id) of pos.payment() have the same label: Currency. [Modules: bs_pos_mcurrancy and point_of_sale] 
2025-05-25 18:54:04,286 29129 DEBUG 1005 odoo.schema: Table 'pos_payment_method': added foreign key 'currency_id' references 'res_currency'('id') ON DELETE set null 
2025-05-25 18:54:04,317 29129 INFO 1005 odoo.modules.loading: loading bs_pos_mcurrancy/views/pos_config_views.xml 
2025-05-25 18:54:04,320 29129 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:54:04,322 29129 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:54:04,359 29129 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:54:04,361 29129 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:54:04,362 29129 WARNING 1005 odoo.addons.base.models.ir_ui_view: Error-prone use of @class in view pos.config.form.multi.currency (bs_pos_mcurrancy.view_pos_config_form_multi_currency): use the hasclass(*classes) function to filter elements by their classes 
2025-05-25 18:54:04,365 29129 INFO 1005 odoo.modules.loading: loading bs_pos_mcurrancy/views/pos_payment_method_views.xml 
2025-05-25 18:54:04,369 29129 WARNING 1005 odoo.modules.loading: Transient module states were reset 
2025-05-25 18:54:04,372 29129 ERROR 1005 odoo.modules.registry: Failed to load registry 
2025-05-25 18:54:04,374 29129 DEBUG 1005 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 67] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:54:04,374 29129 INFO 1005 odoo.modules.loading: loading 1 modules... 
2025-05-25 18:54:04,374 29129 DEBUG 1005 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:54:04,377 29129 DEBUG 1005 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,377 29129 INFO 1005 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:54:04,383 29129 DEBUG 1005 odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:54:04,383 29129 INFO 1005 odoo.modules.loading: loading 161 modules... 
2025-05-25 18:54:04,383 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:54:04,383 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,383 29129 DEBUG 1005 odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:54:04,384 29129 DEBUG 1005 odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,384 29129 DEBUG 1005 odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:54:04,384 29129 DEBUG 1005 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,384 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:54:04,385 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,385 29129 DEBUG 1005 odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:54:04,385 29129 DEBUG 1005 odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,385 29129 DEBUG 1005 odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:54:04,386 29129 DEBUG 1005 odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,387 29129 DEBUG 1005 odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:54:04,388 29129 DEBUG 1005 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,388 29129 DEBUG 1005 odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:54:04,388 29129 DEBUG 1005 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,388 29129 DEBUG 1005 odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:54:04,389 29129 DEBUG 1005 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,389 29129 DEBUG 1005 odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,390 29129 DEBUG 1005 odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:54:04,391 29129 DEBUG 1005 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,391 29129 DEBUG 1005 odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:54:04,393 29129 DEBUG 1005 odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,393 29129 DEBUG 1005 odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:54:04,395 29129 DEBUG 1005 odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,395 29129 DEBUG 1005 odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,396 29129 DEBUG 1005 odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,397 29129 DEBUG 1005 odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:54:04,398 29129 DEBUG 1005 odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,398 29129 DEBUG 1005 odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:54:04,398 29129 DEBUG 1005 odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,398 29129 DEBUG 1005 odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,399 29129 DEBUG 1005 odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,400 29129 DEBUG 1005 odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:54:04,401 29129 DEBUG 1005 odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,401 29129 DEBUG 1005 odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:54:04,401 29129 DEBUG 1005 odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,401 29129 DEBUG 1005 odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:54:04,402 29129 DEBUG 1005 odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,402 29129 DEBUG 1005 odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,403 29129 DEBUG 1005 odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:54:04,404 29129 DEBUG 1005 odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,404 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:54:04,404 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,404 29129 DEBUG 1005 odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:54:04,406 29129 DEBUG 1005 odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,406 29129 DEBUG 1005 odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:54:04,406 29129 DEBUG 1005 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,406 29129 DEBUG 1005 odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:54:04,408 29129 DEBUG 1005 odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,408 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:54:04,408 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,408 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:54:04,409 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,409 29129 DEBUG 1005 odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:54:04,487 29129 DEBUG 1005 odoo.modules.loading: Module stock loaded in 0.08s, 0 queries 
2025-05-25 18:54:04,488 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:54:04,488 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,488 29129 DEBUG 1005 odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:54:04,488 29129 DEBUG 1005 odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,488 29129 DEBUG 1005 odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,489 29129 DEBUG 1005 odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,490 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:54:04,491 29129 DEBUG 1005 odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,492 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,493 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,494 29129 DEBUG 1005 odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:54:04,495 29129 DEBUG 1005 odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,496 29129 DEBUG 1005 odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,498 29129 DEBUG 1005 odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:54:04,499 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,500 29129 DEBUG 1005 odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:54:04,501 29129 DEBUG 1005 odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,501 29129 DEBUG 1005 odoo.modules.loading: Loading module bs_pos_mcurrancy (107/161) 
2025-05-25 18:54:04,501 29129 DEBUG 1005 odoo.modules.loading: Module bs_pos_mcurrancy loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,501 29129 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_extract (108/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_avatax (109/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_barcodelookup (110/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_enterprise (111/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_epson_printer (112/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_hr (113/161) 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,502 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,503 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 18:54:04,504 29129 DEBUG 1005 odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,505 29129 DEBUG 1005 odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,506 29129 DEBUG 1005 odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,507 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,508 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 18:54:04,509 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,510 29129 DEBUG 1005 odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,511 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 18:54:04,512 29129 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:54:04,512 29129 INFO 1005 odoo.modules.loading: 161 modules loaded in 0.13s, 0 queries (+0 extra) 
2025-05-25 18:54:04,620 29129 INFO 1005 odoo.modules.loading: Modules loaded. 
2025-05-25 18:54:04,623 29129 INFO 1005 odoo.modules.registry: Registry loaded in 0.251s 
2025-05-25 18:54:04,625 29129 ERROR 1005 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x7f655f9c2700>, 'point_of_sale.view_pos_payment_method_form')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/odoo/180/odoo/tools/convert.py", line 537, in _tag_root
    f(rec)
  File "/odoo/180/odoo/tools/convert.py", line 400, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/convert.py", line 519, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/convert.py", line 525, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_model.py", line 2253, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/cache.py", line 110, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_model.py", line 2246, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: point_of_sale.view_pos_payment_method_form

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2386, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1913, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1976, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/service/model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "/odoo/180/odoo/http.py", line 1943, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2191, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 740, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/web/controllers/dataset.py", line 42, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_module.py", line 671, in button_immediate_upgrade
    return self._button_immediate_function(self.env.registry[self._name].button_upgrade)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/odoo/180/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/loading.py", line 228, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/odoo/180/odoo/modules/loading.py", line 72, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/odoo/180/odoo/tools/convert.py", line 608, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/odoo/180/odoo/tools/convert.py", line 679, in convert_xml_import
    obj.parse(doc.getroot())
  File "/odoo/180/odoo/tools/convert.py", line 594, in parse
    self._tag_root(de)
  File "/odoo/180/odoo/tools/convert.py", line 550, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /odoo/180/custom/Dev180/bs_pos_mcurrancy/views/pos_payment_method_views.xml:4, somewhere inside
<record id="view_pos_payment_method_form_multi_currency" model="ir.ui.view">
        <field name="name">pos.payment.method.form.multi.currency</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_payment_method_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='journal_setting']" position="after">
                <group string="Multi Currency Settings" name="multi_currency">
                    <field name="use_specific_currency"/>
                    <field name="currency_id" invisible="not use_specific_currency" options="{'no_create': True, 'no_open': True}"/>
                </group>
            </xpath>
        </field>
    </record>
2025-05-25 18:54:04,627 29129 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:54:04] "POST /web/dataset/call_button/ir.module.module/button_immediate_upgrade HTTP/1.1" 200 - 5913 0.948 1.150
