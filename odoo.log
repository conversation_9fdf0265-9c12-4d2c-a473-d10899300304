2025-05-25 17:56:01,642 13061 DEBUG 1005 odoo.api: call pos.order().sync_from_ui([{'message_follower_ids': [], 'message_ids': [], 'website_message_ids': [], 'access_token': '5172b08d-5c82-4a3c-b415-19b86979f537', 'name': 'Order 00064-001-0001', 'last_order_preparation_change': '{"lines":{},"generalNote":"","sittingMode":"dine in"}', 'date_order': '2025-05-25 17:56:01', 'user_id': 2, 'amount_difference': False, 'amount_tax': 753.75, 'amount_total': 5778.75, 'amount_paid': 5778.75, 'amount_return': 0, 'lines': [[0, 0, {'skip_change': False, 'product_id': 16, 'attribute_value_ids': [], 'custom_attribute_value_ids': [], 'price_unit': 25, 'qty': 1, 'price_subtotal': 25, 'price_subtotal_incl': 28.75, 'price_extra': 0, 'price_type': 'original', 'discount': False, 'order_id': 10, 'tax_ids': [[4, 1]], 'pack_lot_ids': [], 'full_product_name': 'Crocheted Poncho Unisize', 'customer_note': False, 'refund_orderline_ids': [], 'refunded_orderline_id': False, 'uuid': '11ca94c7-f833-4316-91d2-61c3a2c30d6f', 'note': '', 'combo_parent_id': False, 'combo_line_ids': [], 'combo_item_id': False, 'write_date': False, 'sale_order_origin_id': False, 'sale_order_line_id': False, 'down_payment_details': False, 'id': 1}], [0, 0, {'skip_change': False, 'product_id': 21, 'attribute_value_ids': [], 'custom_attribute_value_ids': [], 'price_unit': 50, 'qty': 100, 'price_subtotal': 5000, 'price_subtotal_incl': 5750, 'price_extra': 0, 'price_type': 'original', 'discount': False, 'order_id': 10, 'tax_ids': [[4, 1]], 'pack_lot_ids': [], 'full_product_name': 'Cozy Sweater', 'customer_note': False, 'refund_orderline_ids': [], 'refunded_orderline_id': False, 'uuid': 'e93ef9bb-d491-4bf3-b495-8de8eefffa8e', 'note': '', 'combo_parent_id': False, 'combo_line_ids': [], 'combo_item_id': False, 'write_date': False, 'sale_order_origin_id': False, 'sale_order_line_id': False, 'down_payment_details': False, 'id': 2}]], 'company_id': 1, 'pricelist_id': False, 'partner_id': False, 'sequence_number': 1, 'session_id': 64, 'state': 'paid', 'account_move': False, 'picking_ids': [], 'procurement_group_id': False, 'floating_order_name': False, 'general_note': '', 'nb_print': 0, 'pos_reference': '00064-001-0001', 'fiscal_position_id': False, 'payment_ids': [[0, 0, {'name': False, 'pos_order_id': 10, 'amount': 5778.75, 'payment_method_id': 5, 'payment_date': '2025-05-25 17:55:30', 'card_type': False, 'card_brand': False, 'card_no': False, 'cardholder_name': False, 'payment_ref_no': False, 'payment_method_authcode': False, 'payment_method_issuer_bank': False, 'payment_method_payment_mode': False, 'transaction_id': False, 'payment_status': False, 'ticket': '', 'is_change': False, 'account_move_id': False, 'uuid': '93458ec6-c7cc-43a3-8f84-6783a6b01e2f', 'id': 1, 'create_uid': False, 'create_date': False, 'write_uid': False, 'write_date': False, 'payment_currency_id': False, 'payment_currency_rate': False, 'amount_in_payment_currency': False, 'rate_of_currency': False, 'symbol_of_currency': False, 'multi_currency_total': False, 'selected_currency': False, 'online_account_payment_id': False}]], 'to_invoice': False, 'shipping_date': False, 'is_tipped': False, 'tip_amount': False, 'ticket_code': 'hqj87', 'uuid': '982ed262-3443-4ecf-917a-bcd6d6504a45', 'has_deleted_line': False, 'id': '10', 'create_uid': False, 'create_date': False, 'write_uid': False, 'write_date': False, 'employee_id': False, 'payment_currency_id': False, 'payment_currency_rate': False, 'amount_in_payment_currency': False, 'amount_paid_in_payment_currency': False, 'next_online_payment_amount': False, 'table_id': False, 'customer_count': False, 'takeaway': False, 'crm_team_id': False, 'table_stand_number': False}]) 
2025-05-25 17:56:01,642 13061 INFO 1005 odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #******** started for PoS orders references: [{'name': 'Order 00064-001-0001', 'uuid': '982ed262-3443-4ecf-917a-bcd6d6504a45'}] 
2025-05-25 17:56:01,644 13061 DEBUG 1005 odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #******** processing order {'name': 'Order 00064-001-0001', 'uuid': '982ed262-3443-4ecf-917a-bcd6d6504a45'} order full data: {'access_token': '5172b08d-5c82-4a3c-b415-19b86979f537',
 'account_move': False,
 'amount_difference': False,
 'amount_in_payment_currency': False,
 'amount_paid': 5778.75,
 'amount_paid_in_payment_currency': False,
 'amount_return': 0,
 'amount_tax': 753.75,
 'amount_total': 5778.75,
 'company_id': 1,
 'create_date': False,
 'create_uid': False,
 'crm_team_id': False,
 'customer_count': False,
 'date_order': '2025-05-25 17:56:01',
 'employee_id': False,
 'fiscal_position_id': False,
 'floating_order_name': False,
 'general_note': '',
 'has_deleted_line': False,
 'id': '10',
 'is_tipped': False,
 'last_order_preparation_change': '{"lines":{},"generalNote":"","sittingMode":"dine '
                                  'in"}',
 'lines': [[0,
            0,
            {'attribute_value_ids': [],
             'combo_item_id': False,
             'combo_line_ids': [],
             'combo_parent_id': False,
             'custom_attribute_value_ids': [],
             'customer_note': False,
             'discount': False,
             'down_payment_details': False,
             'full_product_name': 'Crocheted Poncho Unisize',
             'id': 1,
             'note': '',
             'order_id': 10,
             'pack_lot_ids': [],
             'price_extra': 0,
             'price_subtotal': 25,
             'price_subtotal_incl': 28.75,
             'price_type': 'original',
             'price_unit': 25,
             'product_id': 16,
             'qty': 1,
             'refund_orderline_ids': [],
             'refunded_orderline_id': False,
             'sale_order_line_id': False,
             'sale_order_origin_id': False,
             'skip_change': False,
             'tax_ids': [[4, 1]],
             'uuid': '11ca94c7-f833-4316-91d2-61c3a2c30d6f',
             'write_date': False}],
           [0,
            0,
            {'attribute_value_ids': [],
             'combo_item_id': False,
             'combo_line_ids': [],
             'combo_parent_id': False,
             'custom_attribute_value_ids': [],
             'customer_note': False,
             'discount': False,
             'down_payment_details': False,
             'full_product_name': 'Cozy Sweater',
             'id': 2,
             'note': '',
             'order_id': 10,
             'pack_lot_ids': [],
             'price_extra': 0,
             'price_subtotal': 5000,
             'price_subtotal_incl': 5750,
             'price_type': 'original',
             'price_unit': 50,
             'product_id': 21,
             'qty': 100,
             'refund_orderline_ids': [],
             'refunded_orderline_id': False,
             'sale_order_line_id': False,
             'sale_order_origin_id': False,
             'skip_change': False,
             'tax_ids': [[4, 1]],
             'uuid': 'e93ef9bb-d491-4bf3-b495-8de8eefffa8e',
             'write_date': False}]],
 'message_follower_ids': [],
 'message_ids': [],
 'name': 'Order 00064-001-0001',
 'nb_print': 0,
 'next_online_payment_amount': False,
 'partner_id': False,
 'payment_currency_id': False,
 'payment_currency_rate': False,
 'payment_ids': [[0,
                  0,
                  {'account_move_id': False,
                   'amount': 5778.75,
                   'amount_in_payment_currency': False,
                   'card_brand': False,
                   'card_no': False,
                   'card_type': False,
                   'cardholder_name': False,
                   'create_date': False,
                   'create_uid': False,
                   'id': 1,
                   'is_change': False,
                   'multi_currency_total': False,
                   'name': False,
                   'online_account_payment_id': False,
                   'payment_currency_id': False,
                   'payment_currency_rate': False,
                   'payment_date': '2025-05-25 17:55:30',
                   'payment_method_authcode': False,
                   'payment_method_id': 5,
                   'payment_method_issuer_bank': False,
                   'payment_method_payment_mode': False,
                   'payment_ref_no': False,
                   'payment_status': False,
                   'pos_order_id': 10,
                   'rate_of_currency': False,
                   'selected_currency': False,
                   'symbol_of_currency': False,
                   'ticket': '',
                   'transaction_id': False,
                   'uuid': '93458ec6-c7cc-43a3-8f84-6783a6b01e2f',
                   'write_date': False,
                   'write_uid': False}]],
 'picking_ids': [],
 'pos_reference': '00064-001-0001',
 'pricelist_id': False,
 'procurement_group_id': False,
 'sequence_number': 1,
 'session_id': 64,
 'shipping_date': False,
 'state': 'paid',
 'table_id': False,
 'table_stand_number': False,
 'takeaway': False,
 'ticket_code': 'hqj87',
 'tip_amount': False,
 'to_invoice': False,
 'user_id': 2,
 'uuid': '982ed262-3443-4ecf-917a-bcd6d6504a45',
 'website_message_ids': [],
 'write_date': False,
 'write_uid': False} 
2025-05-25 17:56:01,665 13061 ERROR 1005 odoo.sql_db: bad query: b'INSERT INTO "pos_payment" ("account_move_id", "amount", "amount_in_payment_currency", "card_brand", "card_no", "card_type", "cardholder_name", "create_date", "create_uid", "is_change", "multi_currency_total", "name", "online_account_payment_id", "payment_currency_id", "payment_currency_rate", "payment_date", "payment_method_authcode", "payment_method_id", "payment_method_issuer_bank", "payment_method_payment_mode", "payment_ref_no", "payment_status", "pos_order_id", "rate_of_currency", "selected_currency", "symbol_of_currency", "ticket", "transaction_id", "uuid", "write_date", "write_uid") VALUES (NULL, \'5778.75\', 0.0, NULL, NULL, NULL, NULL, \'2025-05-25 17:56:01.645765\', 2, false, 0.0, NULL, NULL, NULL, \'0.000000\', \'2025-05-25 17:55:30\', NULL, 5, NULL, NULL, NULL, NULL, 30, 0.0, NULL, NULL, \'\', NULL, \'93458ec6-c7cc-43a3-8f84-6783a6b01e2f\', \'2025-05-25 17:56:01.645765\', 2) RETURNING "id"'
ERROR: column "multi_currency_total" of relation "pos_payment" does not exist
LINE 1: ..._name", "create_date", "create_uid", "is_change", "multi_cur...
                                                             ^
 
2025-05-25 17:56:01,667 13061 ERROR 1005 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2386, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1913, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1976, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/service/model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "/odoo/180/odoo/http.py", line 1943, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2191, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 740, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/web/controllers/dataset.py", line 36, in call_kw
    return call_kw(request.env[model], method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_self_order/models/pos_order.py", line 53, in sync_from_ui
    result = super().sync_from_ui(orders)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_sale/models/pos_order.py", line 51, in sync_from_ui
    data = super().sync_from_ui(orders)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_restaurant/models/pos_order.py", line 33, in sync_from_ui
    result = super().sync_from_ui(orders)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/custom/Ent180/pos_preparation_display/models/pos_order.py", line 11, in sync_from_ui
    data = super().sync_from_ui(orders)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/point_of_sale/models/pos_order.py", line 1052, in sync_from_ui
    order_ids.append(self._process_order(order, False))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/models/pos_order.py", line 87, in _process_order
    result = super()._process_order(order, existing_order)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/point_of_sale/models/pos_order.py", line 91, in _process_order
    pos_order = self.create({
                ^^^^^^^^^^^^^
  File "<decorator-gen-292>", line 2, in create
  File "/odoo/180/odoo/api.py", line 495, in _model_create_multi
    return create(self, [arg])
           ^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_online_payment_self_order/models/pos_order.py", line 25, in create
    return super().create(vals_list)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-155>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/point_of_sale/models/pos_order.py", line 480, in create
    return super().create(vals_list)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-152>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/point_of_sale/models/pos_bus_mixin.py", line 14, in create
    records = super().create(vals_list)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-41>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/mail/models/mail_thread.py", line 278, in create
    threads = super(MailThread, self).create(vals_list)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-0>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/models.py", line 5017, in create
    records = self._create(data_list)
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/models.py", line 5260, in _create
    field.create([
  File "/odoo/180/odoo/fields.py", line 4503, in create
    self.write_batch(record_values, True)
  File "/odoo/180/odoo/fields.py", line 4529, in write_batch
    self.write_real(records_commands_list, create)
  File "/odoo/180/odoo/fields.py", line 4725, in write_real
    flush()
  File "/odoo/180/odoo/fields.py", line 4680, in flush
    comodel.create(to_create)
  File "<decorator-gen-276>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_online_payment/models/pos_payment.py", line 44, in create
    return super().create(vals_list)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-169>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/models/pos_payment.py", line 61, in create
    return super(PosPayment, self).create(vals_list)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-0>", line 2, in create
  File "/odoo/180/odoo/api.py", line 496, in _model_create_multi
    return create(self, arg)
           ^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/models.py", line 5017, in create
    records = self._create(data_list)
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/models.py", line 5201, in _create
    cr.execute(SQL(
  File "/odoo/180/odoo/sql_db.py", line 354, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedColumn: column "multi_currency_total" of relation "pos_payment" does not exist
LINE 1: ..._name", "create_date", "create_uid", "is_change", "multi_cur...
                                                             ^

2025-05-25 17:56:01,671 13061 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:56:01] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 22 0.010 0.022
2025-05-25 17:56:02,680 13061 INFO 1002 odoo.service.server: Initiating server reload 
2025-05-25 17:56:02,911 13061 DEBUG 1002 odoo.service.server: on_close call <bound method Event.set of <threading.Event at 0x7fa1c39600b0: unset>> 
2025-05-25 17:56:02,911 13061 DEBUG 1002 odoo.service.server: on_close call <function _kick_all at 0x7fa1c32344a0> 
2025-05-25 17:56:02,911 13061 DEBUG 1002 odoo.service.server: current thread: <_MainThread(MainThread, started 140332864159872)> 
2025-05-25 17:56:02,911 13061 DEBUG 1002 odoo.service.server: process <_MainThread(MainThread, started 140332864159872)> (False) 
2025-05-25 17:56:02,912 13061 DEBUG 1002 odoo.service.server: process <Thread(odoo.service.cron.cron0, started daemon 140332731209408)> (True) 
2025-05-25 17:56:02,912 13061 DEBUG 1002 odoo.service.server: process <Thread(odoo.service.cron.cron1, started daemon 140332722816704)> (True) 
2025-05-25 17:56:02,912 13061 DEBUG 1002 odoo.service.server: process <Thread(odoo.service.http.request.140332714424000, started 140332714424000)> (False) 
2025-05-25 17:56:02,912 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:02,967 13061 DEBUG 1002 odoo.service.server: process <ImDispatch(odoo.addons.bus.models.bus.Bus, started daemon 140332593235648)> (True) 
2025-05-25 17:56:02,967 13061 DEBUG 1002 odoo.service.server: process <Thread(odoo.service.http.request.140332584842944, started 140332584842944)> (False) 
2025-05-25 17:56:02,967 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,068 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,169 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,271 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,372 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,473 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,574 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,675 13061 DEBUG 1002 odoo.service.server: join and sleep 
2025-05-25 17:56:03,777 13061 INFO 1002 odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 7 connections  
2025-05-25 17:56:03,777 13061 DEBUG 1002 odoo.service.server: -- 
2025-05-25 17:56:10,554 14186 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-05-25 17:56:10,555 14186 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-05-25 17:56:10,555 14186 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-05-25 17:56:10,555 14186 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-05-25 17:56:10,555 14186 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-05-25 17:56:10,555 14186 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-05-25 17:56:10,555 14186 INFO ? odoo: Odoo version 18.0 
2025-05-25 17:56:10,555 14186 INFO ? odoo: Using configuration file at /odoo/180/odoo.conf 
2025-05-25 17:56:10,555 14186 INFO ? odoo: addons paths: ['/odoo/180/odoo/addons', '/root/.local/share/Odoo/addons/18.0', '/odoo/180/addons', '/odoo/180/custom/Ent180', '/odoo/180/custom/Dev180'] 
2025-05-25 17:56:10,555 14186 INFO ? odoo: database: odoo@default:5433 
2025-05-25 17:56:10,606 14186 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-05-25 17:56:10,610 14186 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltoimage binary at /usr/local/bin/wkhtmltoimage 
2025-05-25 17:56:10,715 14186 DEBUG ? odoo.service.server: Setting signal handlers 
2025-05-25 17:56:10,715 14186 INFO ? odoo.service.server: HTTP service (werkzeug) running on BSD.:10184 
2025-05-25 17:56:10,720 14186 DEBUG 1002 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 33] [Cache default: 1] [Cache assets: 1] [Cache templates: 1] [Cache routing: 1] [Cache groups: 1] 
2025-05-25 17:56:10,722 14186 INFO 1002 odoo.modules.loading: loading 1 modules... 
2025-05-25 17:56:10,722 14186 DEBUG 1002 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 17:56:10,727 14186 DEBUG 1002 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 17:56:10,727 14186 INFO 1002 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 17:56:10,739 14186 INFO 1002 odoo.modules.loading: updating modules list 
2025-05-25 17:56:10,740 14186 INFO 1002 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-05-25 17:56:11,786 14186 DEBUG ? odoo.http: HTTP sessions stored in: /root/.local/share/Odoo/sessions 
2025-05-25 17:56:11,864 14186 INFO 1002 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Point of Sale'] to user __system__ #1 via n/a 
2025-05-25 17:56:11,975 14186 DEBUG 1002 odoo.modules.loading: Updating graph with 17 more modules 
2025-05-25 17:56:11,975 14186 INFO 1002 odoo.modules.loading: loading 18 modules... 
2025-05-25 17:56:11,975 14186 DEBUG 1002 odoo.modules.loading: Loading module web (2/18) 
2025-05-25 17:56:11,976 14186 DEBUG 1002 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 17:56:11,976 14186 DEBUG 1002 odoo.modules.loading: Loading module auth_totp (3/18) 
2025-05-25 17:56:11,981 14186 DEBUG 1002 odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 17:56:11,981 14186 DEBUG 1002 odoo.modules.loading: Loading module base_import (4/18) 
2025-05-25 17:56:12,028 14186 DEBUG 1002 odoo.modules.loading: Module base_import loaded in 0.05s, 0 queries 
2025-05-25 17:56:12,028 14186 DEBUG 1002 odoo.modules.loading: Loading module base_import_module (5/18) 
2025-05-25 17:56:12,030 14186 DEBUG 1002 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,030 14186 DEBUG 1002 odoo.modules.loading: Loading module base_setup (6/18) 
2025-05-25 17:56:12,031 14186 DEBUG 1002 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,031 14186 DEBUG 1002 odoo.modules.loading: Loading module bus (7/18) 
2025-05-25 17:56:12,036 14186 DEBUG 1002 odoo.modules.loading: Module bus loaded in 0.01s, 0 queries 
2025-05-25 17:56:12,036 14186 DEBUG 1002 odoo.modules.loading: Loading module web_cohort (8/18) 
2025-05-25 17:56:12,039 14186 DEBUG 1002 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,039 14186 DEBUG 1002 odoo.modules.loading: Loading module web_gantt (9/18) 
2025-05-25 17:56:12,041 14186 DEBUG 1002 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,041 14186 DEBUG 1002 odoo.modules.loading: Loading module web_grid (10/18) 
2025-05-25 17:56:12,042 14186 DEBUG 1002 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,042 14186 DEBUG 1002 odoo.modules.loading: Loading module web_tour (11/18) 
2025-05-25 17:56:12,043 14186 DEBUG 1002 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,043 14186 DEBUG 1002 odoo.modules.loading: Loading module html_editor (12/18) 
2025-05-25 17:56:12,110 14186 DEBUG 1002 odoo.modules.loading: Module html_editor loaded in 0.07s, 0 queries 
2025-05-25 17:56:12,110 14186 DEBUG 1002 odoo.modules.loading: Loading module iap (13/18) 
2025-05-25 17:56:12,110 14186 DEBUG 1002 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,110 14186 DEBUG 1002 odoo.modules.loading: Loading module web_enterprise (14/18) 
2025-05-25 17:56:12,111 14186 DEBUG 1002 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,111 14186 DEBUG 1002 odoo.modules.loading: Loading module web_map (15/18) 
2025-05-25 17:56:12,113 14186 DEBUG 1002 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,113 14186 DEBUG 1002 odoo.modules.loading: Loading module web_editor (16/18) 
2025-05-25 17:56:12,117 14186 DEBUG 1002 odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,118 14186 DEBUG 1002 odoo.modules.loading: Loading module web_mobile (17/18) 
2025-05-25 17:56:12,118 14186 DEBUG 1002 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,118 14186 DEBUG 1002 odoo.modules.loading: Loading module web_unsplash (18/18) 
2025-05-25 17:56:12,119 14186 DEBUG 1002 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 17:56:12,119 14186 INFO 1002 odoo.modules.loading: 18 modules loaded in 0.14s, 0 queries (+0 extra) 
2025-05-25 17:56:12,122 14186 DEBUG 1002 odoo.modules.loading: Updating graph with 79 more modules 
2025-05-25 17:56:12,123 14186 INFO 1002 odoo.modules.loading: loading 97 modules... 
2025-05-25 17:56:12,123 14186 INFO 1002 odoo.modules.loading: Loading module l10n_us (2/97) 
2025-05-25 17:56:12,134 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,137 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,137 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,137 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,137 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,140 14186 INFO 1002 odoo.modules.registry: module l10n_us: creating or updating database tables 
2025-05-25 17:56:12,154 14186 DEBUG 1002 odoo.schema: Table 'res_partner_bank': added column 'aba_routing' of type VARCHAR 
2025-05-25 17:56:12,192 14186 INFO 1002 odoo.modules.loading: loading l10n_us/data/res_company_data.xml 
2025-05-25 17:56:12,196 14186 INFO 1002 odoo.modules.loading: loading l10n_us/views/res_partner_bank_views.xml 
2025-05-25 17:56:12,199 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,214 14186 INFO 1002 odoo.modules.loading: Module l10n_us loaded in 0.09s, 60 queries (+60 other) 
2025-05-25 17:56:12,214 14186 INFO 1002 odoo.modules.loading: Loading module uom (3/97) 
2025-05-25 17:56:12,220 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,221 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,222 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,222 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,222 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,226 14186 INFO 1002 odoo.modules.registry: module uom: creating or updating database tables 
2025-05-25 17:56:12,237 14186 DEBUG 1002 odoo.schema: Table 'uom_category': created 
2025-05-25 17:56:12,242 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': created 
2025-05-25 17:56:12,246 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added constraint 'uom_uom_factor_gt_zero' as CHECK (factor!=0) 
2025-05-25 17:56:12,247 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added constraint 'uom_uom_rounding_gt_zero' as CHECK (rounding>0) 
2025-05-25 17:56:12,248 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added constraint 'uom_uom_factor_reference_is_one' as CHECK((uom_type = 'reference' AND factor = 1.0) OR (uom_type != 'reference')) 
2025-05-25 17:56:12,270 14186 DEBUG 1002 odoo.schema: Table 'uom_category': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,271 14186 DEBUG 1002 odoo.schema: Table 'uom_category': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,273 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added foreign key 'category_id' references 'uom_category'('id') ON DELETE restrict 
2025-05-25 17:56:12,275 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,276 14186 DEBUG 1002 odoo.schema: Table 'uom_uom': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,283 14186 INFO 1002 odoo.modules.loading: loading uom/data/uom_data.xml 
2025-05-25 17:56:12,318 14186 INFO 1002 odoo.modules.loading: loading uom/security/uom_security.xml 
2025-05-25 17:56:12,324 14186 DEBUG 1002 odoo.modules.registry: Invalidating groups model caches from create /odoo/180/odoo/addons/base/models/res_users.py:1481 
2025-05-25 17:56:12,325 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:12,326 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:12,335 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/res_users.py:1654 
2025-05-25 17:56:12,335 14186 DEBUG 1002 odoo.modules.registry: Invalidating groups model caches from _update_xmlids /odoo/180/odoo/addons/base/models/ir_model.py:2375 
2025-05-25 17:56:12,335 14186 INFO 1002 odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-05-25 17:56:12,341 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:12,345 14186 INFO 1002 odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-05-25 17:56:12,347 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,351 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,354 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,357 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,359 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,360 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,364 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,366 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,368 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,369 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,376 14186 INFO 1002 odoo.modules.loading: Module uom loaded in 0.16s, 299 queries (+299 other) 
2025-05-25 17:56:12,376 14186 INFO 1002 odoo.modules.loading: Loading module barcodes (6/97) 
2025-05-25 17:56:12,384 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,386 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,386 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,386 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,387 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,390 14186 INFO 1002 odoo.modules.registry: module barcodes: creating or updating database tables 
2025-05-25 17:56:12,398 14186 DEBUG 1002 odoo.schema: Table 'barcode_nomenclature': created 
2025-05-25 17:56:12,403 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': created 
2025-05-25 17:56:12,409 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'nomenclature_id' of type int4 
2025-05-25 17:56:12,435 14186 DEBUG 1002 odoo.schema: Table 'barcode_nomenclature': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,437 14186 DEBUG 1002 odoo.schema: Table 'barcode_nomenclature': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,438 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added foreign key 'barcode_nomenclature_id' references 'barcode_nomenclature'('id') ON DELETE set null 
2025-05-25 17:56:12,440 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,441 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,442 14186 DEBUG 1002 odoo.schema: Table 'res_company': added foreign key 'nomenclature_id' references 'barcode_nomenclature'('id') ON DELETE set null 
2025-05-25 17:56:12,448 14186 INFO 1002 odoo.modules.loading: loading barcodes/data/barcodes_data.xml 
2025-05-25 17:56:12,452 14186 INFO 1002 odoo.modules.loading: loading barcodes/views/barcodes_view.xml 
2025-05-25 17:56:12,454 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,458 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,460 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,462 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,462 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,465 14186 INFO 1002 odoo.modules.loading: loading barcodes/security/ir.model.access.csv 
2025-05-25 17:56:12,469 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:12,479 14186 INFO 1002 odoo.modules.loading: Module barcodes loaded in 0.10s, 149 queries (+149 other) 
2025-05-25 17:56:12,479 14186 INFO 1002 odoo.modules.loading: Loading module http_routing (11/97) 
2025-05-25 17:56:12,485 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,487 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,487 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,487 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,487 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,491 14186 INFO 1002 odoo.modules.registry: module http_routing: creating or updating database tables 
2025-05-25 17:56:12,507 14186 INFO 1002 odoo.modules.loading: loading http_routing/views/http_routing_template.xml 
2025-05-25 17:56:12,510 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,514 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,516 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,517 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,519 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,521 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,523 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,525 14186 INFO 1002 odoo.modules.loading: loading http_routing/views/res_lang_views.xml 
2025-05-25 17:56:12,527 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,532 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,540 14186 INFO 1002 odoo.modules.loading: Module http_routing loaded in 0.06s, 100 queries (+100 other) 
2025-05-25 17:56:12,540 14186 INFO 1002 odoo.modules.loading: Loading module onboarding (12/97) 
2025-05-25 17:56:12,546 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,548 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,548 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,548 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,548 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,552 14186 INFO 1002 odoo.modules.registry: module onboarding: creating or updating database tables 
2025-05-25 17:56:12,557 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress': created 
2025-05-25 17:56:12,562 14186 DEBUG 1002 odoo.schema: Create table 'onboarding_progress_onboarding_progress_step_rel': m2m relation between 'onboarding_progress' and 'onboarding_progress_step' 
2025-05-25 17:56:12,570 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding': created 
2025-05-25 17:56:12,575 14186 DEBUG 1002 odoo.schema: Create table 'onboarding_onboarding_onboarding_onboarding_step_rel': m2m relation between 'onboarding_onboarding' and 'onboarding_onboarding_step' 
2025-05-25 17:56:12,577 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding': added constraint 'onboarding_onboarding_route_name_uniq' as UNIQUE (route_name) 
2025-05-25 17:56:12,582 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding_step': created 
2025-05-25 17:56:12,588 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_step': created 
2025-05-25 17:56:12,609 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_onboarding_progress_step_rel': added foreign key 'onboarding_progress_id' references 'onboarding_progress'('id') ON DELETE cascade 
2025-05-25 17:56:12,611 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_onboarding_progress_step_rel': added foreign key 'onboarding_progress_step_id' references 'onboarding_progress_step'('id') ON DELETE cascade 
2025-05-25 17:56:12,612 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress': added foreign key 'company_id' references 'res_company'('id') ON DELETE cascade 
2025-05-25 17:56:12,614 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress': added foreign key 'onboarding_id' references 'onboarding_onboarding'('id') ON DELETE cascade 
2025-05-25 17:56:12,615 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,617 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,619 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding_onboarding_onboarding_step_rel': added foreign key 'onboarding_onboarding_id' references 'onboarding_onboarding'('id') ON DELETE cascade 
2025-05-25 17:56:12,620 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding_onboarding_onboarding_step_rel': added foreign key 'onboarding_onboarding_step_id' references 'onboarding_onboarding_step'('id') ON DELETE cascade 
2025-05-25 17:56:12,622 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,623 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,624 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding_step': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,626 14186 DEBUG 1002 odoo.schema: Table 'onboarding_onboarding_step': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,628 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_step': added foreign key 'step_id' references 'onboarding_onboarding_step'('id') ON DELETE cascade 
2025-05-25 17:56:12,629 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_step': added foreign key 'company_id' references 'res_company'('id') ON DELETE cascade 
2025-05-25 17:56:12,631 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_step': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,632 14186 DEBUG 1002 odoo.schema: Table 'onboarding_progress_step': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,638 14186 INFO 1002 odoo.modules.loading: loading onboarding/views/onboarding_templates.xml 
2025-05-25 17:56:12,640 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,642 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,645 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,646 14186 INFO 1002 odoo.modules.loading: loading onboarding/views/onboarding_views.xml 
2025-05-25 17:56:12,647 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,650 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,652 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,654 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,656 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,657 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,658 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,658 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,659 14186 INFO 1002 odoo.modules.loading: loading onboarding/views/onboarding_menus.xml 
2025-05-25 17:56:12,662 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,665 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,668 14186 INFO 1002 odoo.modules.loading: loading onboarding/security/ir.model.access.csv 
2025-05-25 17:56:12,674 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:12,684 14186 INFO 1002 odoo.modules.loading: Module onboarding loaded in 0.14s, 225 queries (+225 other) 
2025-05-25 17:56:12,684 14186 INFO 1002 odoo.modules.loading: Loading module resource (13/97) 
2025-05-25 17:56:12,694 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,695 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,696 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,696 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,696 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,700 14186 INFO 1002 odoo.modules.registry: module resource: creating or updating database tables 
2025-05-25 17:56:12,705 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'resource_calendar_id' of type int4 
2025-05-25 17:56:12,714 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar': created 
2025-05-25 17:56:12,720 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': created 
2025-05-25 17:56:12,726 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': created 
2025-05-25 17:56:12,732 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': created 
2025-05-25 17:56:12,734 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added constraint 'resource_resource_check_time_efficiency' as CHECK(time_efficiency>0) 
2025-05-25 17:56:12,766 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': created index 'resource_calendar_attendance__dayofweek_index' ("dayofweek") 
2025-05-25 17:56:12,768 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': created index 'resource_calendar_attendance__hour_from_index' ("hour_from") 
2025-05-25 17:56:12,770 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': created index 'resource_calendar_leaves__calendar_id_index' ("calendar_id") 
2025-05-25 17:56:12,772 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': created index 'resource_calendar_leaves__resource_id_index' ("resource_id") 
2025-05-25 17:56:12,774 14186 DEBUG 1002 odoo.schema: Table 'res_company': added foreign key 'resource_calendar_id' references 'resource_calendar'('id') ON DELETE restrict 
2025-05-25 17:56:12,775 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar': added foreign key 'company_id' references 'res_company'('id') ON DELETE set null 
2025-05-25 17:56:12,777 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,778 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,779 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': added foreign key 'calendar_id' references 'resource_calendar'('id') ON DELETE cascade 
2025-05-25 17:56:12,781 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': added foreign key 'resource_id' references 'resource_resource'('id') ON DELETE set null 
2025-05-25 17:56:12,782 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,783 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_attendance': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,785 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': added foreign key 'company_id' references 'res_company'('id') ON DELETE set null 
2025-05-25 17:56:12,786 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': added foreign key 'calendar_id' references 'resource_calendar'('id') ON DELETE set null 
2025-05-25 17:56:12,787 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': added foreign key 'resource_id' references 'resource_resource'('id') ON DELETE set null 
2025-05-25 17:56:12,789 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,790 14186 DEBUG 1002 odoo.schema: Table 'resource_calendar_leaves': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,791 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added foreign key 'company_id' references 'res_company'('id') ON DELETE set null 
2025-05-25 17:56:12,793 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added foreign key 'user_id' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,794 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added foreign key 'calendar_id' references 'resource_calendar'('id') ON DELETE set null 
2025-05-25 17:56:12,796 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added foreign key 'create_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,798 14186 DEBUG 1002 odoo.schema: Table 'resource_resource': added foreign key 'write_uid' references 'res_users'('id') ON DELETE set null 
2025-05-25 17:56:12,804 14186 INFO 1002 odoo.modules.loading: loading resource/data/resource_data.xml 
2025-05-25 17:56:12,812 14186 DEBUG 1002 odoo.api: call res.company()._init_data_resource_calendar() 
2025-05-25 17:56:12,813 14186 INFO 1002 odoo.modules.loading: loading resource/security/ir.model.access.csv 
2025-05-25 17:56:12,818 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:12,820 14186 INFO 1002 odoo.modules.loading: loading resource/security/resource_security.xml 
2025-05-25 17:56:12,827 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_rule.py:190 
2025-05-25 17:56:12,830 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_rule.py:190 
2025-05-25 17:56:12,831 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_rule.py:190 
2025-05-25 17:56:12,833 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_rule.py:190 
2025-05-25 17:56:12,835 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_rule.py:190 
2025-05-25 17:56:12,835 14186 INFO 1002 odoo.modules.loading: loading resource/views/resource_resource_views.xml 
2025-05-25 17:56:12,837 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,842 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,844 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,846 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,847 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,847 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,848 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,849 14186 INFO 1002 odoo.modules.loading: loading resource/views/resource_calendar_leaves_views.xml 
2025-05-25 17:56:12,850 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,853 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,856 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,858 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,860 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,860 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,861 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,861 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,862 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,863 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,863 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,864 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,865 14186 INFO 1002 odoo.modules.loading: loading resource/views/resource_calendar_attendance_views.xml 
2025-05-25 17:56:12,866 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,869 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,870 14186 INFO 1002 odoo.modules.loading: loading resource/views/resource_calendar_views.xml 
2025-05-25 17:56:12,871 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,874 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,877 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,879 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:352 
2025-05-25 17:56:12,880 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_actions.py:108 
2025-05-25 17:56:12,880 14186 INFO 1002 odoo.modules.loading: loading resource/views/menuitems.xml 
2025-05-25 17:56:12,881 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,883 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,885 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,886 14186 DEBUG 1002 odoo.modules.registry: Invalidating default model caches from create /odoo/180/odoo/addons/base/models/ir_ui_menu.py:166 
2025-05-25 17:56:12,888 14186 INFO 1002 odoo.modules.loading: Module resource: loading demo 
2025-05-25 17:56:12,888 14186 INFO 1002 odoo.modules.loading: loading resource/data/resource_demo.xml 
2025-05-25 17:56:12,907 14186 INFO 1002 odoo.modules.loading: Module resource loaded in 0.22s, 425 queries (+425 other) 
2025-05-25 17:56:12,908 14186 INFO 1002 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (18/97) 
2025-05-25 17:56:12,913 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:12,915 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:12,915 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:12,915 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:12,915 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:12,919 14186 INFO 1002 odoo.modules.registry: module barcodes_gs1_nomenclature: creating or updating database tables 
2025-05-25 17:56:12,921 14186 DEBUG 1002 odoo.schema: Table 'barcode_nomenclature': added column 'gs1_separator_fnc1' of type VARCHAR 
2025-05-25 17:56:12,921 14186 DEBUG 1002 odoo.models: Table 'barcode_nomenclature': setting default value of new column gs1_separator_fnc1 to '(Alt029|#|\\x1D)' 
2025-05-25 17:56:12,922 14186 DEBUG 1002 odoo.schema: Table 'barcode_nomenclature': added column 'is_gs1_nomenclature' of type bool 
2025-05-25 17:56:12,924 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added column 'associated_uom_id' of type int4 
2025-05-25 17:56:12,924 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added column 'gs1_content_type' of type VARCHAR 
2025-05-25 17:56:12,924 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added column 'gs1_decimal_usage' of type bool 
2025-05-25 17:56:12,937 14186 DEBUG 1002 odoo.schema: Table 'barcode_rule': added foreign key 'associated_uom_id' references 'uom_uom'('id') ON DELETE set null 
2025-05-25 17:56:12,942 14186 INFO 1002 odoo.modules.loading: loading barcodes_gs1_nomenclature/data/barcodes_gs1_rules.xml 
2025-05-25 17:56:12,974 14186 INFO 1002 odoo.modules.loading: loading barcodes_gs1_nomenclature/views/barcodes_view.xml 
2025-05-25 17:56:12,976 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,983 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,985 14186 DEBUG 1002 odoo.modules.registry: Invalidating templates model caches from create /odoo/180/odoo/addons/base/models/ir_ui_view.py:517 
2025-05-25 17:56:12,993 14186 INFO 1002 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.09s, 195 queries (+195 other) 
2025-05-25 17:56:12,993 14186 INFO 1002 odoo.modules.loading: Loading module mail (23/97) 
2025-05-25 17:56:13,009 14186 DEBUG 1002 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-05-25 17:56:13,011 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-05-25 17:56:13,011 14186 DEBUG 1002 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-05-25 17:56:13,011 14186 DEBUG 1002 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-05-25 17:56:13,012 14186 DEBUG 1002 odoo.models: Patching iap.account.description with translate=True 
2025-05-25 17:56:13,021 14186 INFO 1002 odoo.modules.registry: module mail: creating or updating database tables 
2025-05-25 17:56:13,026 14186 DEBUG 1002 odoo.schema: Table 'mail_alias': created 
2025-05-25 17:56:13,034 14186 DEBUG 1002 odoo.schema: Table 'mail_alias_domain': created 
2025-05-25 17:56:13,037 14186 DEBUG 1002 odoo.schema: Table 'mail_alias_domain': added constraint 'mail_alias_domain_bounce_email_uniques' as UNIQUE(bounce_alias, name) 
2025-05-25 17:56:13,039 14186 DEBUG 1002 odoo.schema: Table 'mail_alias_domain': added constraint 'mail_alias_domain_catchall_email_uniques' as UNIQUE(catchall_alias, name) 
2025-05-25 17:56:13,046 14186 DEBUG 1002 odoo.schema: Table 'fetchmail_server': created 
2025-05-25 17:56:13,054 14186 DEBUG 1002 odoo.schema: Table 'mail_notification': created 
2025-05-25 17:56:13,056 14186 DEBUG 1002 odoo.schema: Table 'mail_notification': added constraint 'mail_notification_notification_partner_required' as CHECK(notification_type NOT IN ('email', 'inbox') OR res_partner_id IS NOT NULL) 
2025-05-25 17:56:13,067 14186 DEBUG 1002 odoo.schema: Table 'mail_activity_type': created 
2025-05-25 17:56:13,073 14186 DEBUG 1002 odoo.schema: Create table 'mail_activity_rel': m2m relation between 'mail_activity_type' and 'mail_activity_type' 
2025-05-25 17:56:13,077 14186 DEBUG 1002 odoo.schema: Create table 'mail_activity_type_mail_template_rel': m2m relation between 'mail_activity_type' and 'mail_template' 
2025-05-25 17:56:13,082 14186 DEBUG 1002 odoo.schema: Table 'mail_activity': created 
2025-05-25 17:56:13,087 14186 DEBUG 1002 odoo.schema: Create table 'activity_attachment_rel': m2m relation between 'mail_activity' and 'ir_attachment' 
2025-05-25 17:56:13,088 14186 DEBUG 1002 odoo.schema: Table 'mail_activity': added constraint 'mail_activity_check_res_id_is_set' as CHECK(res_id IS NOT NULL AND res_id !=0 ) 
2025-05-25 17:56:13,093 14186 DEBUG 1002 odoo.schema: Table 'mail_activity_plan': created 
2025-05-25 17:56:13,098 14186 DEBUG 1002 odoo.schema: Table 'mail_activity_plan_template': created 
2025-05-25 17:56:13,104 14186 DEBUG 1002 odoo.schema: Table 'mail_blacklist': created 
2025-05-25 17:56:13,108 14186 DEBUG 1002 odoo.schema: Table 'mail_blacklist': added constraint 'mail_blacklist_unique_email' as unique (email) 
2025-05-25 17:56:13,112 14186 DEBUG 1002 odoo.schema: Table 'mail_followers': created 
2025-05-25 17:56:13,118 14186 DEBUG 1002 odoo.schema: Create table 'mail_followers_mail_message_subtype_rel': m2m relation between 'mail_followers' and 'mail_message_subtype' 
2025-05-25 17:56:13,120 14186 DEBUG 1002 odoo.schema: Table 'mail_followers': added constraint 'mail_followers_mail_followers_res_partner_res_model_id_uniq' as unique(res_model,res_id,partner_id) 
2025-05-25 17:56:13,125 14186 DEBUG 1002 odoo.schema: Table 'mail_gateway_allowed': created 
2025-05-25 17:56:13,132 14186 DEBUG 1002 odoo.schema: Table 'mail_link_preview': created 
2025-05-25 17:56:13,138 14186 DEBUG 1002 odoo.schema: Table 'mail_message_reaction': created 
2025-05-25 17:56:13,139 14186 DEBUG 1002 odoo.schema: Table 'mail_message_reaction': added constraint 'mail_message_reaction_partner_or_guest_exists' as CHECK((partner_id IS NOT NULL AND guest_id IS NULL) OR (partner_id IS NULL AND guest_id IS NOT NULL)) 
2025-05-25 17:56:13,148 14186 DEBUG 1002 odoo.schema: Table 'mail_message_schedule': created 
2025-05-25 17:56:13,154 14186 DEBUG 1002 odoo.schema: Table 'mail_message_subtype': created 
2025-05-25 17:56:13,159 14186 DEBUG 1002 odoo.schema: Table 'mail_message_translation': created 
2025-05-25 17:56:13,169 14186 DEBUG 1002 odoo.schema: Table 'mail_message': created 
2025-05-25 17:56:13,174 14186 DEBUG 1002 odoo.schema: Create table 'message_attachment_rel': m2m relation between 'mail_message' and 'ir_attachment' 
2025-05-25 17:56:13,179 14186 DEBUG 1002 odoo.schema: Create table 'mail_message_res_partner_rel': m2m relation between 'mail_message' and 'res_partner' 
2025-05-25 17:56:13,184 14186 DEBUG 1002 odoo.schema: Create table 'mail_message_res_partner_starred_rel': m2m relation between 'mail_message' and 'res_partner' 
2025-05-25 17:56:13,192 14186 DEBUG 1002 odoo.schema: Table 'mail_mail': created 
2025-05-25 17:56:13,198 14186 DEBUG 1002 odoo.schema: Create table 'mail_mail_res_partner_rel': m2m relation between 'mail_mail' and 'res_partner' 
2025-05-25 17:56:13,206 14186 DEBUG 1002 odoo.schema: Table 'mail_push': created 
2025-05-25 17:56:13,212 14186 DEBUG 1002 odoo.schema: Table 'mail_push_device': created 
2025-05-25 17:56:13,216 14186 DEBUG 1002 odoo.schema: Table 'mail_push_device': added constraint 'mail_push_device_endpoint_unique' as unique(endpoint) 
2025-05-25 17:56:13,222 14186 DEBUG 1002 odoo.schema: Table 'mail_scheduled_message': created 
2025-05-25 17:56:13,227 14186 DEBUG 1002 odoo.schema: Create table 'scheduled_message_attachment_rel': m2m relation between 'mail_scheduled_message' and 'ir_attachment' 
2025-05-25 17:56:13,231 14186 DEBUG 1002 odoo.schema: Create table 'mail_scheduled_message_res_partner_rel': m2m relation between 'mail_scheduled_message' and 'res_partner' 
2025-05-25 17:56:13,236 14186 DEBUG 1002 odoo.schema: Table 'mail_tracking_value': created 
2025-05-25 17:56:13,242 14186 DEBUG 1002 odoo.schema: Table 'mail_template': created 
2025-05-25 17:56:13,246 14186 DEBUG 1002 odoo.schema: Create table 'email_template_attachment_rel': m2m relation between 'mail_template' and 'ir_attachment' 
2025-05-25 17:56:13,251 14186 DEBUG 1002 odoo.schema: Create table 'mail_template_ir_actions_report_rel': m2m relation between 'mail_template' and 'ir_act_report_xml' 
2025-05-25 17:56:13,255 14186 DEBUG 1002 odoo.schema: Table 'mail_ice_server': created 
2025-05-25 17:56:13,262 14186 DEBUG 1002 odoo.schema: Table 'mail_canned_response': created 
2025-05-25 17:56:13,267 14186 DEBUG 1002 odoo.schema: Create table 'mail_canned_response_res_groups_rel': m2m relation between 'mail_canned_response' and 'res_groups' 
2025-05-25 17:56:13,270 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'voice_active_duration' of type int4 
2025-05-25 17:56:13,271 14186 DEBUG 1002 odoo.models: Table 'res_users_settings': setting default value of new column voice_active_duration to 200 
2025-05-25 17:56:13,271 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'push_to_talk_key' of type VARCHAR 
2025-05-25 17:56:13,271 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'channel_notifications' of type VARCHAR 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'is_discuss_sidebar_category_channel_open' of type bool 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.models: Table 'res_users_settings': setting default value of new column is_discuss_sidebar_category_channel_open to True 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'is_discuss_sidebar_category_chat_open' of type bool 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.models: Table 'res_users_settings': setting default value of new column is_discuss_sidebar_category_chat_open to True 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'use_push_to_talk' of type bool 
2025-05-25 17:56:13,272 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings': added column 'mute_until_dt' of type timestamp 
2025-05-25 17:56:13,275 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings_volumes': created 
2025-05-25 17:56:13,277 14186 DEBUG 1002 odoo.schema: Table 'res_users_settings_volumes': added constraint 'res_users_settings_volumes_partner_or_guest_exists' as CHECK((partner_id IS NOT NULL AND guest_id IS NULL) OR (partner_id IS NULL AND guest_id IS NOT NULL)) 
2025-05-25 17:56:13,284 14186 DEBUG 1002 odoo.schema: Table 'bus_presence': added column 'guest_id' of type int4 
2025-05-25 17:56:13,285 14186 DEBUG 1002 odoo.schema: Table 'bus_presence': added constraint 'bus_presence_partner_or_guest_exists' as CHECK((user_id IS NOT NULL AND guest_id IS NULL) OR (user_id IS NULL AND guest_id IS NOT NULL)) 
2025-05-25 17:56:13,296 14186 DEBUG 1002 odoo.schema: Create table 'ir_act_server_res_partner_rel': m2m relation between 'ir_act_server' and 'res_partner' 
2025-05-25 17:56:13,296 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'template_id' of type int4 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_type_id' of type int4 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_date_deadline_range' of type int4 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_user_id' of type int4 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'mail_post_method' of type VARCHAR 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_summary' of type VARCHAR 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_date_deadline_range_type' of type VARCHAR 
2025-05-25 17:56:13,297 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_user_type' of type VARCHAR 
2025-05-25 17:56:13,298 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_user_field_name' of type VARCHAR 
2025-05-25 17:56:13,298 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'activity_note' of type text 
2025-05-25 17:56:13,298 14186 DEBUG 1002 odoo.schema: Table 'ir_act_server': added column 'mail_post_autofollow' of type bool 
2025-05-25 17:56:13,298 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.template_id 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-05-25 17:56:13,299 14186 INFO 1002 odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-05-25 17:56:13,312 14186 DEBUG 1002 odoo.schema: Table 'ir_model': added column 'is_mail_thread' of type bool 
2025-05-25 17:56:13,312 14186 DEBUG 1002 odoo.schema: Table 'ir_model': added column 'is_mail_activity' of type bool 
2025-05-25 17:56:13,312 14186 DEBUG 1002 odoo.schema: Table 'ir_model': added column 'is_mail_blacklist' of type bool 
2025-05-25 17:56:13,316 14186 DEBUG 1002 odoo.schema: Table 'ir_model_fields': added column 'tracking' of type int4 
2025-05-25 17:56:13,327 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'alias_domain_id' of type int4 
2025-05-25 17:56:13,328 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'alias_domain_name' of type VARCHAR 
2025-05-25 17:56:13,328 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'email_primary_color' of type VARCHAR 
2025-05-25 17:56:13,328 14186 DEBUG 1002 odoo.schema: Table 'res_company': added column 'email_secondary_color' of type VARCHAR 
2025-05-25 17:56:13,328 14186 INFO 1002 odoo.models: Prepare computation of res.company.email_primary_color 
2025-05-25 17:56:13,329 14186 INFO 1002 odoo.models: Prepare computation of res.company.email_secondary_color 
2025-05-25 17:56:13,332 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'tenor_gif_limit' of type int4 
2025-05-25 17:56:13,332 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'twilio_account_sid' of type VARCHAR 
2025-05-25 17:56:13,332 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'twilio_account_token' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'sfu_server_url' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'sfu_server_key' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'tenor_api_key' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'tenor_content_filter' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'google_translate_api_key' of type VARCHAR 
2025-05-25 17:56:13,333 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'external_email_server_default' of type bool 
2025-05-25 17:56:13,334 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'module_google_gmail' of type bool 
2025-05-25 17:56:13,334 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'module_microsoft_outlook' of type bool 
2025-05-25 17:56:13,334 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'restrict_template_rendering' of type bool 
2025-05-25 17:56:13,334 14186 DEBUG 1002 odoo.schema: Table 'res_config_settings': added column 'use_twilio_rtc_servers' of type bool 
2025-05-25 17:56:20,612 4357 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 17:56:28,353 14186 ERROR 1002 odoo.sql_db: bad query: b'ALTER TABLE "res_users" ADD COLUMN "notification_type" VARCHAR ; COMMENT ON COLUMN "res_users"."notification_type" IS \'Notification\''
ERROR: canceling statement due to lock timeout
 
2025-05-25 17:56:28,358 14186 WARNING 1002 odoo.modules.loading: Transient module states were reset 
2025-05-25 17:56:28,358 14186 ERROR 1002 odoo.modules.registry: Failed to load registry 
2025-05-25 17:56:28,359 14186 CRITICAL 1002 odoo.service.server: Failed to initialize database `1002`. 
Traceback (most recent call last):
  File "/odoo/180/odoo/service/server.py", line 1329, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/odoo/180/odoo/modules/loading.py", line 484, in load_modules
    processed_modules += load_marked_modules(env, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/modules/loading.py", line 206, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "/odoo/180/odoo/modules/registry.py", line 601, in init_models
    model._auto_init()
  File "/odoo/180/odoo/models.py", line 3468, in _auto_init
    new = field.update_db(self, columns)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/fields.py", line 1089, in update_db
    self.update_db_column(model, column)
  File "/odoo/180/odoo/fields.py", line 1119, in update_db_column
    sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
  File "/odoo/180/odoo/tools/sql.py", line 329, in create_column
    cr.execute(sql)
  File "/odoo/180/odoo/sql_db.py", line 354, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.LockNotAvailable: canceling statement due to lock timeout

2025-05-25 17:56:28,362 14186 DEBUG 1002 odoo.service.server: cron0 started! 
2025-05-25 17:56:28,362 14186 DEBUG 1002 odoo.service.server: cron1 started! 
2025-05-25 17:56:28,367 14186 DEBUG ? odoo.modules.registry: Multiprocess load registry signaling: [Registry: 59] [Cache default: 8] [Cache assets: 1] [Cache templates: 3] [Cache routing: 1] [Cache groups: 4] 
2025-05-25 17:56:28,368 14186 INFO ? odoo.modules.loading: loading 1 modules... 
2025-05-25 17:56:28,368 14186 DEBUG ? odoo.modules.loading: Loading module base (1/1) 
2025-05-25 17:56:28,372 14186 DEBUG ? odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,372 14186 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 17:56:28,378 14186 DEBUG ? odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 17:56:28,378 14186 INFO ? odoo.modules.loading: loading 161 modules... 
2025-05-25 17:56:28,379 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 17:56:28,379 14186 DEBUG ? odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,379 14186 DEBUG ? odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 17:56:28,379 14186 DEBUG ? odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,379 14186 DEBUG ? odoo.modules.loading: Loading module web (4/161) 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,380 14186 DEBUG ? odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,381 14186 DEBUG ? odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,382 14186 DEBUG ? odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 17:56:28,384 14186 DEBUG ? odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,385 14186 DEBUG ? odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 17:56:28,385 14186 DEBUG ? odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,385 14186 DEBUG ? odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 17:56:28,386 14186 DEBUG ? odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,386 14186 DEBUG ? odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 17:56:28,387 14186 DEBUG ? odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,387 14186 DEBUG ? odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,388 14186 DEBUG ? odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,389 14186 DEBUG ? odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 17:56:28,392 14186 DEBUG ? odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,392 14186 DEBUG ? odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 17:56:28,394 14186 DEBUG ? odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,394 14186 DEBUG ? odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 17:56:28,394 14186 DEBUG ? odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,394 14186 DEBUG ? odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 17:56:28,396 14186 DEBUG ? odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,396 14186 DEBUG ? odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 17:56:28,397 14186 DEBUG ? odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,397 14186 DEBUG ? odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 17:56:28,398 14186 DEBUG ? odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,398 14186 DEBUG ? odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 17:56:28,399 14186 DEBUG ? odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,399 14186 DEBUG ? odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 17:56:28,400 14186 DEBUG ? odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,400 14186 DEBUG ? odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 17:56:28,401 14186 DEBUG ? odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,401 14186 DEBUG ? odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 17:56:28,402 14186 DEBUG ? odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,402 14186 DEBUG ? odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 17:56:28,402 14186 DEBUG ? odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,402 14186 DEBUG ? odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 17:56:28,406 14186 DEBUG ? odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,406 14186 DEBUG ? odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 17:56:28,407 14186 DEBUG ? odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,407 14186 DEBUG ? odoo.modules.loading: Loading module product (38/161) 
2025-05-25 17:56:28,416 14186 DEBUG ? odoo.modules.loading: Module product loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,416 14186 DEBUG ? odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 17:56:28,417 14186 DEBUG ? odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,417 14186 DEBUG ? odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 17:56:28,418 14186 DEBUG ? odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,419 14186 DEBUG ? odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 17:56:28,419 14186 DEBUG ? odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,419 14186 DEBUG ? odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 17:56:28,419 14186 DEBUG ? odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,419 14186 DEBUG ? odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 17:56:28,420 14186 DEBUG ? odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,421 14186 DEBUG ? odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 17:56:28,422 14186 DEBUG ? odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,422 14186 DEBUG ? odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 17:56:28,426 14186 DEBUG ? odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,426 14186 DEBUG ? odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 17:56:28,427 14186 DEBUG ? odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,427 14186 DEBUG ? odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 17:56:28,433 14186 DEBUG ? odoo.modules.loading: Module sms loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,433 14186 DEBUG ? odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 17:56:28,436 14186 DEBUG ? odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,436 14186 DEBUG ? odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 17:56:28,436 14186 DEBUG ? odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,437 14186 DEBUG ? odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 17:56:28,438 14186 DEBUG ? odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,438 14186 DEBUG ? odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 17:56:28,444 14186 DEBUG ? odoo.modules.loading: Module payment loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,444 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 17:56:28,445 14186 DEBUG ? odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,446 14186 DEBUG ? odoo.modules.loading: Loading module account (53/161) 
2025-05-25 17:56:28,529 14186 DEBUG ? odoo.modules.loading: Module account loaded in 0.08s, 0 queries 
2025-05-25 17:56:28,529 14186 DEBUG ? odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 17:56:28,529 14186 DEBUG ? odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,529 14186 DEBUG ? odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 17:56:28,537 14186 DEBUG ? odoo.modules.loading: Module hr loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,537 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 17:56:28,538 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,538 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 17:56:28,540 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,540 14186 DEBUG ? odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 17:56:28,558 14186 DEBUG ? odoo.modules.loading: Module stock loaded in 0.02s, 0 queries 
2025-05-25 17:56:28,559 14186 DEBUG ? odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 17:56:28,564 14186 DEBUG ? odoo.modules.loading: Module account_accountant loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,564 14186 DEBUG ? odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 17:56:28,566 14186 DEBUG ? odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,566 14186 DEBUG ? odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 17:56:28,568 14186 DEBUG ? odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,568 14186 DEBUG ? odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 17:56:28,572 14186 DEBUG ? odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,572 14186 DEBUG ? odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 17:56:28,573 14186 DEBUG ? odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,573 14186 DEBUG ? odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 17:56:28,575 14186 DEBUG ? odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,575 14186 DEBUG ? odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 17:56:28,576 14186 DEBUG ? odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,576 14186 DEBUG ? odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 17:56:28,577 14186 DEBUG ? odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,577 14186 DEBUG ? odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 17:56:28,582 14186 DEBUG ? odoo.modules.loading: Module hr_expense loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,582 14186 DEBUG ? odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 17:56:28,583 14186 DEBUG ? odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,583 14186 DEBUG ? odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 17:56:28,583 14186 DEBUG ? odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,583 14186 DEBUG ? odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 17:56:28,584 14186 DEBUG ? odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,584 14186 DEBUG ? odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,587 14186 DEBUG ? odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 17:56:28,598 14186 DEBUG ? odoo.modules.loading: Module purchase loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,598 14186 DEBUG ? odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 17:56:28,599 14186 DEBUG ? odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,599 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 17:56:28,600 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,600 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 17:56:28,600 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,600 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 17:56:28,601 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,601 14186 DEBUG ? odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 17:56:28,608 14186 DEBUG ? odoo.modules.loading: Module stock_account loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,608 14186 DEBUG ? odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 17:56:28,614 14186 DEBUG ? odoo.modules.loading: Module stock_barcode loaded in 0.01s, 0 queries 
2025-05-25 17:56:28,614 14186 DEBUG ? odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 17:56:28,615 14186 DEBUG ? odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,615 14186 DEBUG ? odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 17:56:28,616 14186 DEBUG ? odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,616 14186 DEBUG ? odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 17:56:28,618 14186 DEBUG ? odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,618 14186 DEBUG ? odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 17:56:28,619 14186 DEBUG ? odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,619 14186 DEBUG ? odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 17:56:28,620 14186 DEBUG ? odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,620 14186 DEBUG ? odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 17:56:28,624 14186 DEBUG ? odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,624 14186 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 17:56:28,625 14186 DEBUG ? odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,625 14186 DEBUG ? odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 17:56:28,626 14186 DEBUG ? odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,626 14186 DEBUG ? odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 17:56:28,630 14186 DEBUG ? odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,630 14186 DEBUG ? odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 17:56:28,630 14186 DEBUG ? odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,631 14186 DEBUG ? odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 17:56:28,631 14186 DEBUG ? odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,631 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 17:56:28,632 14186 DEBUG ? odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,632 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 17:56:28,633 14186 DEBUG ? odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,633 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 17:56:28,634 14186 DEBUG ? odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 17:56:28,634 14186 INFO ? odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 17:56:28,710 4357 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 17:56:28,820 14186 INFO ? odoo.modules.registry: module point_of_sale: creating or updating database tables 
2025-05-25 17:56:28,889 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_mode': dropped constraint NOT NULL 
2025-05-25 17:56:28,890 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_service_mode': dropped constraint NOT NULL 
2025-05-25 17:56:28,890 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_pay_after': dropped constraint NOT NULL 
2025-05-25 17:56:28,967 14186 DEBUG ? odoo.schema: Table 'product_template': column 'sale_line_warn': dropped constraint NOT NULL 
2025-05-25 17:56:29,016 14186 DEBUG ? odoo.schema: Table 'res_company': column 'account_tax_periodicity_reminder_day': dropped constraint NOT NULL 
2025-05-25 17:56:29,017 14186 DEBUG ? odoo.schema: Table 'res_company': column 'account_tax_periodicity': dropped constraint NOT NULL 
2025-05-25 17:56:29,017 14186 DEBUG ? odoo.schema: Table 'res_company': column 'security_lead': dropped constraint NOT NULL 
2025-05-25 17:56:29,024 14186 DEBUG ? odoo.schema: Table 'res_config_settings': column 'default_picking_policy': dropped constraint NOT NULL 
2025-05-25 17:56:29,419 14186 INFO ? odoo.modules.loading: loading point_of_sale/security/point_of_sale_security.xml 
2025-05-25 17:56:29,425 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,426 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/res_users.py:1667 
2025-05-25 17:56:29,426 14186 DEBUG ? odoo.modules.registry: Invalidating groups model caches from _update_xmlids /odoo/180/odoo/addons/base/models/ir_model.py:2375 
2025-05-25 17:56:29,430 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,563 14186 DEBUG ? odoo.modules.registry: Invalidating groups model caches from write /odoo/180/odoo/addons/base/models/res_users.py:1528 
2025-05-25 17:56:29,563 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/res_users.py:1667 
2025-05-25 17:56:29,564 14186 DEBUG ? odoo.modules.registry: Invalidating groups model caches from _update_xmlids /odoo/180/odoo/addons/base/models/ir_model.py:2375 
2025-05-25 17:56:29,574 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,579 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,582 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,586 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,588 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,590 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,593 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,594 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,597 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,600 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,603 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,605 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:29,615 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,615 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/res_users.py:797 
2025-05-25 17:56:29,622 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,622 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/res_users.py:797 
2025-05-25 17:56:29,623 14186 INFO ? odoo.modules.loading: loading point_of_sale/security/ir.model.access.csv 
2025-05-25 17:56:29,655 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,658 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,658 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,659 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,659 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,660 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,660 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,661 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,661 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,662 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,662 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,663 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,663 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,663 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,664 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,664 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,665 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,665 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,665 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,666 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,666 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,667 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,667 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,668 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,668 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,669 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,669 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,670 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,670 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,670 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,671 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,671 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,672 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,672 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,672 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,673 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,673 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,674 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,674 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,674 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,675 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,676 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,676 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,677 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,677 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,678 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,678 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,678 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,679 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,679 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,680 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,680 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,681 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,681 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,682 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,683 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,683 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,684 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,684 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,684 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,685 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,685 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,685 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:29,687 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/default_barcode_patterns.xml 
2025-05-25 17:56:29,692 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/digest_data.xml 
2025-05-25 17:56:29,696 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/pos_note_data.xml 
2025-05-25 17:56:29,701 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/mail_template_data.xml 
2025-05-25 17:56:29,705 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/point_of_sale_tour.xml 
2025-05-25 17:56:29,708 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/ir_config_parameter_data.xml 
2025-05-25 17:56:29,708 14186 DEBUG ? odoo.api: call pos.config()._set_default_pos_load_limit() 
2025-05-25 17:56:29,709 14186 INFO ? odoo.modules.loading: loading point_of_sale/wizard/pos_details.xml 
2025-05-25 17:56:29,711 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,712 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,715 14186 INFO ? odoo.modules.loading: loading point_of_sale/wizard/pos_payment.xml 
2025-05-25 17:56:29,718 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,718 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,722 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,723 14186 INFO ? odoo.modules.loading: loading point_of_sale/wizard/pos_close_session_wizard.xml 
2025-05-25 17:56:29,725 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,726 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,728 14186 INFO ? odoo.modules.loading: loading point_of_sale/wizard/pos_daily_sales_reports.xml 
2025-05-25 17:56:29,730 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,731 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,734 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,734 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_assets_index.xml 
2025-05-25 17:56:29,737 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,737 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,739 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/point_of_sale_report.xml 
2025-05-25 17:56:29,743 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,744 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,744 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/point_of_sale_view.xml 
2025-05-25 17:56:29,746 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,752 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,755 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,756 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,758 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,760 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,762 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,764 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,765 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,766 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_note_view.xml 
2025-05-25 17:56:29,768 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,770 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,773 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,774 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,776 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_order_view.xml 
2025-05-25 17:56:29,779 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,779 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,789 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,789 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,791 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,792 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,796 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,798 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,799 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,800 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,804 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,804 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,807 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,808 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,810 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,813 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,813 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,816 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,816 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,821 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,822 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,823 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,824 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,824 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,827 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,828 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,829 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,833 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,834 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,840 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,842 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,843 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_category_view.xml 
2025-05-25 17:56:29,846 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,846 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,850 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,851 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,852 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,853 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,855 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,856 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/product_combo_views.xml 
2025-05-25 17:56:29,859 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,860 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,865 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/product_view.xml 
2025-05-25 17:56:29,867 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,868 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,875 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,878 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,881 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,883 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,884 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,896 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,897 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,911 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,913 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,915 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,917 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,920 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,923 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,924 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,928 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,936 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,939 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,940 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,946 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,947 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,952 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,953 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,958 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,959 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,960 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/account_journal_view.xml 
2025-05-25 17:56:29,963 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,965 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_payment_method_views.xml 
2025-05-25 17:56:29,968 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,968 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,973 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,973 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,976 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,976 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,980 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,981 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:29,985 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:29,985 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_payment_views.xml 
2025-05-25 17:56:29,988 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,988 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,991 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,992 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,994 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,995 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:29,999 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,000 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,002 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_config_view.xml 
2025-05-25 17:56:30,005 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,005 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,010 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,010 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,013 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,013 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,017 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,020 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,021 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,024 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,026 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,028 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,029 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_bill_view.xml 
2025-05-25 17:56:30,031 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,032 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,035 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,036 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,038 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,039 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,041 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_session_view.xml 
2025-05-25 17:56:30,044 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,045 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,050 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,051 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,053 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,053 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,055 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,055 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,059 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,065 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,068 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/point_of_sale_sequence.xml 
2025-05-25 17:56:30,071 14186 INFO ? odoo.modules.loading: loading point_of_sale/data/point_of_sale_data.xml 
2025-05-25 17:56:30,072 14186 DEBUG ? odoo.api: call stock.warehouse()._create_missing_pos_picking_types() 
2025-05-25 17:56:30,074 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,074 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,075 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,075 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,109 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_order_report_view.xml 
2025-05-25 17:56:30,111 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,112 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,115 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,115 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,119 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,119 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,122 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,122 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,129 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,132 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,133 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,135 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,136 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/account_statement_view.xml 
2025-05-25 17:56:30,139 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,145 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,156 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/digest_views.xml 
2025-05-25 17:56:30,159 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,161 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,166 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/res_partner_view.xml 
2025-05-25 17:56:30,169 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,170 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,188 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,189 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,194 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,194 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/report_userlabel.xml 
2025-05-25 17:56:30,197 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,198 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,199 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/report_saledetails.xml 
2025-05-25 17:56:30,202 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,202 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,204 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,205 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,207 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,207 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,209 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/point_of_sale_dashboard.xml 
2025-05-25 17:56:30,211 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,213 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,215 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,216 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,217 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,220 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/report_invoice.xml 
2025-05-25 17:56:30,223 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,224 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,227 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_printer_view.xml 
2025-05-25 17:56:30,230 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,230 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,235 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,236 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,236 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,239 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:30,242 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_ticket_view.xml 
2025-05-25 17:56:30,245 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,246 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,248 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,249 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,250 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/res_config_settings_views.xml 
2025-05-25 17:56:30,254 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,255 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,284 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/customer_display_index.xml 
2025-05-25 17:56:30,288 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,288 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,289 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/account_move_views.xml 
2025-05-25 17:56:30,291 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,294 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,316 14186 INFO ? odoo.modules.loading: loading point_of_sale/views/pos_session_sales_details.xml 
2025-05-25 17:56:30,320 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,320 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,325 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,325 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:30,345 14186 INFO ? odoo.modules.loading: Module point_of_sale loaded in 1.71s, 2267 queries (+2267 other) 
2025-05-25 17:56:30,345 14186 DEBUG ? odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 17:56:30,346 14186 DEBUG ? odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,346 14186 DEBUG ? odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 17:56:30,351 14186 DEBUG ? odoo.modules.loading: Module purchase_stock loaded in 0.01s, 0 queries 
2025-05-25 17:56:30,351 14186 DEBUG ? odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 17:56:30,361 14186 DEBUG ? odoo.modules.loading: Module sale loaded in 0.01s, 0 queries 
2025-05-25 17:56:30,361 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,362 14186 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 17:56:30,363 14186 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,363 14186 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 17:56:30,364 14186 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,364 14186 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 17:56:30,366 14186 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,366 14186 DEBUG ? odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 17:56:30,366 14186 DEBUG ? odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,366 14186 DEBUG ? odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 17:56:30,381 14186 DEBUG ? odoo.modules.loading: Module account_reports loaded in 0.01s, 0 queries 
2025-05-25 17:56:30,381 14186 DEBUG ? odoo.modules.loading: Loading module hr_expense_extract (107/161) 
2025-05-25 17:56:30,383 14186 DEBUG ? odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,383 14186 DEBUG ? odoo.modules.loading: Loading module pos_avatax (108/161) 
2025-05-25 17:56:30,384 14186 DEBUG ? odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 17:56:30,384 14186 INFO ? odoo.modules.loading: Loading module pos_barcodelookup (109/161) 
2025-05-25 17:56:30,542 14186 INFO ? odoo.modules.loading: loading pos_barcodelookup/views/product_view.xml 
2025-05-25 17:56:30,546 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,628 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,642 14186 INFO ? odoo.modules.loading: Module pos_barcodelookup loaded in 0.26s, 39 queries (+39 other) 
2025-05-25 17:56:30,642 14186 INFO ? odoo.modules.loading: Loading module pos_enterprise (110/161) 
2025-05-25 17:56:30,795 14186 INFO ? odoo.modules.registry: module pos_enterprise: creating or updating database tables 
2025-05-25 17:56:30,828 14186 INFO ? odoo.modules.loading: loading pos_enterprise/views/res_config_settings_views.xml 
2025-05-25 17:56:30,831 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,852 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:30,898 14186 INFO ? odoo.modules.loading: Module pos_enterprise loaded in 0.26s, 125 queries (+125 other) 
2025-05-25 17:56:30,898 14186 INFO ? odoo.modules.loading: Loading module pos_epson_printer (111/161) 
2025-05-25 17:56:31,118 14186 INFO ? odoo.modules.registry: module pos_epson_printer: creating or updating database tables 
2025-05-25 17:56:31,151 14186 INFO ? odoo.modules.loading: loading pos_epson_printer/views/pos_config_views.xml 
2025-05-25 17:56:31,157 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,178 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,185 14186 INFO ? odoo.modules.loading: loading pos_epson_printer/views/res_config_settings_views.xml 
2025-05-25 17:56:31,188 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,189 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,224 14186 INFO ? odoo.modules.loading: loading pos_epson_printer/views/pos_printer_views.xml 
2025-05-25 17:56:31,227 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,228 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,238 14186 INFO ? odoo.modules.loading: Module pos_epson_printer loaded in 0.34s, 157 queries (+157 other) 
2025-05-25 17:56:31,238 14186 INFO ? odoo.modules.loading: Loading module pos_hr (112/161) 
2025-05-25 17:56:31,386 14186 INFO ? odoo.modules.registry: module pos_hr: creating or updating database tables 
2025-05-25 17:56:31,490 14186 INFO ? odoo.modules.loading: loading pos_hr/views/pos_config.xml 
2025-05-25 17:56:31,494 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,513 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,523 14186 INFO ? odoo.modules.loading: loading pos_hr/views/pos_order_view.xml 
2025-05-25 17:56:31,526 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,527 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,538 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,539 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,542 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,543 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,546 14186 INFO ? odoo.modules.loading: loading pos_hr/views/pos_payment_view.xml 
2025-05-25 17:56:31,549 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,550 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,552 14186 INFO ? odoo.modules.loading: loading pos_hr/views/pos_order_report_view.xml 
2025-05-25 17:56:31,554 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,555 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,559 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,560 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,562 14186 INFO ? odoo.modules.loading: loading pos_hr/views/single_employee_sales_report.xml 
2025-05-25 17:56:31,565 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,567 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,570 14186 INFO ? odoo.modules.loading: loading pos_hr/views/multi_employee_sales_report.xml 
2025-05-25 17:56:31,572 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:31,574 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,575 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,576 14186 INFO ? odoo.modules.loading: loading pos_hr/views/res_config_settings_views.xml 
2025-05-25 17:56:31,578 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,579 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,617 14186 INFO ? odoo.modules.loading: loading pos_hr/wizard/pos_daily_sales_reports.xml 
2025-05-25 17:56:31,621 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,622 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:31,636 14186 INFO ? odoo.modules.loading: Module pos_hr loaded in 0.40s, 347 queries (+347 other) 
2025-05-25 17:56:31,636 14186 INFO ? odoo.modules.loading: Loading module pos_multi_currency_payment (113/161) 
2025-05-25 17:56:31,794 14186 ERROR ? odoo.tests.common: Importing test framework, avoid importing from business modules and when not running in test mode 
Stack (most recent call last):
  File "/usr/lib/python3.12/threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "/usr/lib/python3.12/threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "/usr/lib/python3.12/threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "/usr/lib/python3.12/socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "/usr/lib/python3.12/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/usr/lib/python3.12/socketserver.py", line 761, in __init__
    self.handle()
  File "/odoo/180/venv/lib/python3.12/site-packages/werkzeug/serving.py", line 390, in handle
    super().handle()
  File "/usr/lib/python3.12/http/server.py", line 436, in handle
    self.handle_one_request()
  File "/usr/lib/python3.12/http/server.py", line 424, in handle_one_request
    method()
  File "/odoo/180/venv/lib/python3.12/site-packages/werkzeug/serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "/odoo/180/venv/lib/python3.12/site-packages/werkzeug/serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "/odoo/180/odoo/http.py", line 2386, in __call__
    response = request._serve_db()
  File "/odoo/180/odoo/http.py", line 1890, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/odoo/180/odoo/http.py", line 1500, in _open_registry
    registry = Registry(self.db)
  File "/odoo/180/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "/odoo/180/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/odoo/180/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/odoo/180/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/odoo/180/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/odoo/180/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/odoo/180/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/__init__.py", line 3, in <module>
    from . import tests
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/tests/__init__.py", line 1, in <module>
    from . import test_pos_multi_currency_payment
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/tests/test_pos_multi_currency_payment.py", line 2, in <module>
    from odoo.tests import tagged
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/odoo/tests/__init__.py", line 8, in <module>
    from . import common
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/odoo/tests/common.py", line 100, in <module>
    _logger.error(
2025-05-25 17:56:31,942 14186 INFO ? odoo.modules.registry: module pos_multi_currency_payment: creating or updating database tables 
2025-05-25 17:56:31,951 14186 DEBUG ? odoo.schema: Table 'pos_payment': added column 'foreign_currency_id' of type int4 
2025-05-25 17:56:31,951 14186 DEBUG ? odoo.schema: Table 'pos_payment': added column 'symbol_of_currency' of type VARCHAR 
2025-05-25 17:56:31,952 14186 DEBUG ? odoo.schema: Table 'pos_payment': added column 'selected_currency' of type VARCHAR 
2025-05-25 17:56:31,952 14186 DEBUG ? odoo.schema: Table 'pos_payment': added column 'multi_currency_total' of type numeric 
2025-05-25 17:56:31,952 14186 DEBUG ? odoo.schema: Table 'pos_payment': added column 'rate_of_currency' of type double precision 
2025-05-25 17:56:31,952 14186 INFO ? odoo.models: Prepare computation of pos.payment.foreign_currency_id 
2025-05-25 17:56:31,979 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (rate_of_currency, currency_rate) of pos.payment() have the same label: Conversion Rate. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:31,979 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (symbol_of_currency, currency_id) of pos.payment() have the same label: Currency. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:32,057 14186 DEBUG ? odoo.schema: Table 'pos_payment': added foreign key 'foreign_currency_id' references 'res_currency'('id') ON DELETE set null 
2025-05-25 17:56:32,065 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/security/ir.model.access.csv 
2025-05-25 17:56:32,069 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,070 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,071 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/views/pos_payment_method_views.xml 
2025-05-25 17:56:32,073 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,075 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,082 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,083 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,086 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/views/pos_config_views.xml 
2025-05-25 17:56:32,087 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,088 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,122 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/views/pos_session_views.xml 
2025-05-25 17:56:32,124 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,125 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,129 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/views/pos_order_views.xml 
2025-05-25 17:56:32,132 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,133 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,144 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,145 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,148 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/views/pos_session_sales_by_currency_views.xml 
2025-05-25 17:56:32,151 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,152 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:32,154 14186 INFO ? odoo.modules.loading: loading pos_multi_currency_payment/report/pos_order_report_views.xml 
2025-05-25 17:56:32,156 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,158 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,161 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,162 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,164 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,165 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,177 14186 INFO ? odoo.modules.loading: Module pos_multi_currency_payment loaded in 0.54s, 338 queries (+338 other) 
2025-05-25 17:56:32,177 14186 INFO ? odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 17:56:32,408 14186 INFO ? odoo.modules.registry: module pos_online_payment: creating or updating database tables 
2025-05-25 17:56:32,430 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (rate_of_currency, currency_rate) of pos.payment() have the same label: Conversion Rate. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:32,430 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (symbol_of_currency, currency_id) of pos.payment() have the same label: Currency. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:32,468 14186 INFO ? odoo.modules.loading: loading pos_online_payment/views/payment_transaction_views.xml 
2025-05-25 17:56:32,471 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,489 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,496 14186 INFO ? odoo.modules.loading: loading pos_online_payment/views/pos_payment_views.xml 
2025-05-25 17:56:32,499 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,499 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,503 14186 INFO ? odoo.modules.loading: loading pos_online_payment/views/pos_payment_method_views.xml 
2025-05-25 17:56:32,506 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,507 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,513 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,514 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,518 14186 INFO ? odoo.modules.loading: loading pos_online_payment/views/payment_portal_templates.xml 
2025-05-25 17:56:32,522 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,522 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,525 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,525 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,527 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,527 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,528 14186 INFO ? odoo.modules.loading: loading pos_online_payment/views/account_payment_views.xml 
2025-05-25 17:56:32,530 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,531 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,547 14186 INFO ? odoo.modules.loading: Module pos_online_payment loaded in 0.37s, 219 queries (+219 other) 
2025-05-25 17:56:32,547 14186 INFO ? odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 17:56:32,708 14186 INFO ? odoo.modules.registry: module pos_preparation_display: creating or updating database tables 
2025-05-25 17:56:32,758 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/security/ir.model.access.csv 
2025-05-25 17:56:32,771 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,788 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,789 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,789 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,790 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,790 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,791 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,791 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:32,792 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/security/preparation_display_security.xml 
2025-05-25 17:56:32,796 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_rule.py:200 
2025-05-25 17:56:32,797 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/views/preparation_display_assets_index.xml 
2025-05-25 17:56:32,800 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,801 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,802 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/views/preparation_display_view.xml 
2025-05-25 17:56:32,806 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,808 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,810 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,811 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,813 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,813 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,819 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,819 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,821 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,821 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,824 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,824 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,828 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:32,831 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:32,838 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/wizard/preparation_display_reset_wizard.xml 
2025-05-25 17:56:32,841 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,841 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:32,844 14186 INFO ? odoo.modules.loading: loading pos_preparation_display/data/preparation_display_cron.xml 
2025-05-25 17:56:32,850 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,851 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,852 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,853 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,856 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,856 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:32,868 14186 INFO ? odoo.modules.loading: Module pos_preparation_display loaded in 0.32s, 244 queries (+244 other) 
2025-05-25 17:56:32,868 14186 INFO ? odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 17:56:33,101 14186 INFO ? odoo.modules.registry: module pos_restaurant: creating or updating database tables 
2025-05-25 17:56:33,116 14186 DEBUG ? odoo.schema: Table 'restaurant_table': column 'identifier': dropped constraint NOT NULL 
2025-05-25 17:56:33,128 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (rate_of_currency, currency_rate) of pos.payment() have the same label: Conversion Rate. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:33,128 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (symbol_of_currency, currency_id) of pos.payment() have the same label: Currency. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:33,175 14186 INFO ? odoo.modules.loading: loading pos_restaurant/security/ir.model.access.csv 
2025-05-25 17:56:33,185 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:33,201 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:33,201 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:33,202 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:33,203 14186 INFO ? odoo.modules.loading: loading pos_restaurant/views/pos_order_views.xml 
2025-05-25 17:56:33,206 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,208 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,220 14186 INFO ? odoo.modules.loading: loading pos_restaurant/views/pos_restaurant_views.xml 
2025-05-25 17:56:33,222 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,223 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,227 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,227 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,229 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,229 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,231 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,231 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,235 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:33,236 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,236 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,239 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:33,241 14186 INFO ? odoo.modules.loading: loading pos_restaurant/views/res_config_settings_views.xml 
2025-05-25 17:56:33,245 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,246 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,289 14186 INFO ? odoo.modules.loading: Module pos_restaurant loaded in 0.42s, 279 queries (+279 other) 
2025-05-25 17:56:33,289 14186 INFO ? odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 17:56:33,447 14186 INFO ? odoo.modules.registry: module pos_sms: creating or updating database tables 
2025-05-25 17:56:33,492 14186 INFO ? odoo.modules.loading: loading pos_sms/data/sms_data.xml 
2025-05-25 17:56:33,512 14186 INFO ? odoo.modules.loading: loading pos_sms/views/res_config_settings_views.xml 
2025-05-25 17:56:33,514 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,519 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:33,627 14186 INFO ? odoo.modules.loading: loading pos_sms/data/point_of_sale_data.xml 
2025-05-25 17:56:33,639 14186 INFO ? odoo.modules.loading: Module pos_sms loaded in 0.35s, 148 queries (+148 other) 
2025-05-25 17:56:33,639 14186 DEBUG ? odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 17:56:33,640 14186 DEBUG ? odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,640 14186 DEBUG ? odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 17:56:33,641 14186 DEBUG ? odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,641 14186 DEBUG ? odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 17:56:33,642 14186 DEBUG ? odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,642 14186 DEBUG ? odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 17:56:33,643 14186 DEBUG ? odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,643 14186 DEBUG ? odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 17:56:33,646 14186 DEBUG ? odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,646 14186 DEBUG ? odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 17:56:33,647 14186 DEBUG ? odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,647 14186 DEBUG ? odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 17:56:33,648 14186 DEBUG ? odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,648 14186 DEBUG ? odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 17:56:33,652 14186 DEBUG ? odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,652 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 17:56:33,653 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,653 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 17:56:33,653 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,653 14186 DEBUG ? odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 17:56:33,657 14186 DEBUG ? odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,657 14186 DEBUG ? odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 17:56:33,658 14186 DEBUG ? odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,658 14186 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 17:56:33,659 14186 DEBUG ? odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,659 14186 DEBUG ? odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 17:56:33,660 14186 DEBUG ? odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,660 14186 DEBUG ? odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 17:56:33,662 14186 DEBUG ? odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,662 14186 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 17:56:33,664 14186 DEBUG ? odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,664 14186 DEBUG ? odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 17:56:33,667 14186 DEBUG ? odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,668 14186 DEBUG ? odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 17:56:33,668 14186 DEBUG ? odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,668 14186 DEBUG ? odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 17:56:33,669 14186 DEBUG ? odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 17:56:33,669 14186 INFO ? odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 17:56:33,866 14186 INFO ? odoo.modules.loading: Module pos_hr_mobile loaded in 0.20s, 15 queries (+15 other) 
2025-05-25 17:56:33,866 14186 INFO ? odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 17:56:34,035 14186 INFO ? odoo.modules.registry: module pos_hr_preparation_display: creating or updating database tables 
2025-05-25 17:56:34,155 14186 INFO ? odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.29s, 29 queries (+29 other) 
2025-05-25 17:56:34,155 14186 INFO ? odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 17:56:34,337 14186 INFO ? odoo.modules.loading: Module pos_hr_restaurant loaded in 0.18s, 15 queries (+15 other) 
2025-05-25 17:56:34,337 14186 INFO ? odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 17:56:34,493 14186 INFO ? odoo.modules.registry: module pos_restaurant_preparation_display: creating or updating database tables 
2025-05-25 17:56:34,517 14186 INFO ? odoo.modules.loading: loading pos_restaurant_preparation_display/views/preparation_display_view.xml 
2025-05-25 17:56:34,520 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_ui_menu.py:173 
2025-05-25 17:56:34,616 14186 INFO ? odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.28s, 70 queries (+70 other) 
2025-05-25 17:56:34,617 14186 DEBUG ? odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 17:56:34,620 14186 DEBUG ? odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 17:56:34,620 14186 INFO ? odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 17:56:34,801 14186 INFO ? odoo.modules.registry: module pos_self_order: creating or updating database tables 
2025-05-25 17:56:34,809 14186 DEBUG ? odoo.models: Table 'pos_config': setting default value of new column self_ordering_mode to 'nothing' 
2025-05-25 17:56:34,809 14186 DEBUG ? odoo.models: Table 'pos_config': setting default value of new column self_ordering_service_mode to 'counter' 
2025-05-25 17:56:34,809 14186 DEBUG ? odoo.models: Table 'pos_config': setting default value of new column self_ordering_pay_after to 'meal' 
2025-05-25 17:56:34,828 14186 DEBUG ? odoo.models: Table 'product_template': setting default value of new column sale_line_warn to 'no-message' 
2025-05-25 17:56:34,879 14186 DEBUG ? odoo.models: Table 'res_company': setting default value of new column account_tax_periodicity_reminder_day to 7 
2025-05-25 17:56:34,879 14186 DEBUG ? odoo.models: Table 'res_company': setting default value of new column account_tax_periodicity to 'monthly' 
2025-05-25 17:56:34,879 14186 DEBUG ? odoo.models: Table 'res_company': setting default value of new column security_lead to 0.0 
2025-05-25 17:56:34,927 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (rate_of_currency, currency_rate) of pos.payment() have the same label: Conversion Rate. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:34,927 14186 WARNING ? odoo.addons.base.models.ir_model: Two fields (symbol_of_currency, currency_id) of pos.payment() have the same label: Currency. [Modules: pos_multi_currency_payment and point_of_sale] 
2025-05-25 17:56:35,140 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_mode': added constraint NOT NULL 
2025-05-25 17:56:35,141 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_service_mode': added constraint NOT NULL 
2025-05-25 17:56:35,141 14186 DEBUG ? odoo.schema: Table 'pos_config': column 'self_ordering_pay_after': added constraint NOT NULL 
2025-05-25 17:56:35,142 14186 DEBUG ? odoo.schema: Table 'restaurant_table': column 'identifier': added constraint NOT NULL 
2025-05-25 17:56:35,144 14186 DEBUG ? odoo.schema: Table 'product_template': column 'sale_line_warn': added constraint NOT NULL 
2025-05-25 17:56:35,145 14186 DEBUG ? odoo.schema: Table 'res_config_settings': column 'default_picking_policy': added constraint NOT NULL 
2025-05-25 17:56:35,149 14186 DEBUG ? odoo.schema: Table 'res_company': column 'account_tax_periodicity_reminder_day': added constraint NOT NULL 
2025-05-25 17:56:35,150 14186 DEBUG ? odoo.schema: Table 'res_company': column 'account_tax_periodicity': added constraint NOT NULL 
2025-05-25 17:56:35,151 14186 DEBUG ? odoo.schema: Table 'res_company': column 'security_lead': added constraint NOT NULL 
2025-05-25 17:56:35,179 14186 INFO ? odoo.modules.loading: loading pos_self_order/security/ir.model.access.csv 
2025-05-25 17:56:35,189 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:35,190 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods /odoo/180/odoo/addons/base/models/ir_model.py:2144 
2025-05-25 17:56:35,190 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/pos_self_order.index.xml 
2025-05-25 17:56:35,194 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,195 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,198 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/qr_code.xml 
2025-05-25 17:56:35,201 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:35,206 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,206 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,207 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/pos_category_views.xml 
2025-05-25 17:56:35,209 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,210 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,215 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/pos_config_view.xml 
2025-05-25 17:56:35,217 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,218 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,220 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/pos_session_view.xml 
2025-05-25 17:56:35,222 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,223 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,227 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/custom_link_views.xml 
2025-05-25 17:56:35,229 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,229 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,232 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/pos_restaurant_views.xml 
2025-05-25 17:56:35,235 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,236 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,239 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/product_views.xml 
2025-05-25 17:56:35,241 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,242 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,249 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,250 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,260 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,261 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,265 14186 INFO ? odoo.modules.loading: loading pos_self_order/data/init_access.xml 
2025-05-25 17:56:35,267 14186 DEBUG ? odoo.api: call restaurant.table()._update_identifier() 
2025-05-25 17:56:35,268 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/res_config_settings_views.xml 
2025-05-25 17:56:35,271 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,272 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,406 14186 INFO ? odoo.modules.loading: loading pos_self_order/views/point_of_sale_dashboard.xml 
2025-05-25 17:56:35,410 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,411 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,415 14186 DEBUG ? odoo.modules.registry: Invalidating default model caches from write /odoo/180/odoo/addons/base/models/ir_actions.py:114 
2025-05-25 17:56:35,417 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,418 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:35,439 14186 INFO ? odoo.modules.loading: Module pos_self_order loaded in 0.82s, 840 queries (+840 other) 
2025-05-25 17:56:35,439 14186 DEBUG ? odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 17:56:35,441 14186 DEBUG ? odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,441 14186 DEBUG ? odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 17:56:35,443 14186 DEBUG ? odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,443 14186 DEBUG ? odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 17:56:35,444 14186 DEBUG ? odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,444 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 17:56:35,444 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,444 14186 INFO ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 17:56:35,633 14186 INFO ? odoo.modules.loading: loading spreadsheet_dashboard_pos_hr/data/dashboards.xml 
2025-05-25 17:56:35,681 14186 INFO ? odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.24s, 40 queries (+40 other) 
2025-05-25 17:56:35,681 14186 INFO ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 17:56:35,857 14186 INFO ? odoo.modules.loading: loading spreadsheet_dashboard_pos_restaurant/data/dashboards.xml 
2025-05-25 17:56:35,973 14186 INFO ? odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.29s, 39 queries (+39 other) 
2025-05-25 17:56:35,973 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 17:56:35,975 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,975 14186 DEBUG ? odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 17:56:35,975 14186 DEBUG ? odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,976 14186 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 17:56:35,976 14186 DEBUG ? odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,976 14186 DEBUG ? odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 17:56:35,979 14186 DEBUG ? odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 17:56:35,979 14186 INFO ? odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 17:56:36,157 14186 INFO ? odoo.modules.registry: module pos_online_payment_self_order: creating or updating database tables 
2025-05-25 17:56:36,210 14186 INFO ? odoo.modules.loading: loading pos_online_payment_self_order/views/res_config_settings_views.xml 
2025-05-25 17:56:36,216 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,237 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,290 14186 INFO ? odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.31s, 157 queries (+157 other) 
2025-05-25 17:56:36,290 14186 INFO ? odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 17:56:36,532 14186 INFO ? odoo.modules.registry: module pos_order_tracking_display: creating or updating database tables 
2025-05-25 17:56:36,555 14186 INFO ? odoo.modules.loading: loading pos_order_tracking_display/views/index.xml 
2025-05-25 17:56:36,567 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,588 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,590 14186 INFO ? odoo.modules.loading: loading pos_order_tracking_display/views/preparation_display_view.xml 
2025-05-25 17:56:36,594 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,596 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:36,613 14186 INFO ? odoo.modules.loading: Module pos_order_tracking_display loaded in 0.32s, 74 queries (+74 other) 
2025-05-25 17:56:36,613 14186 INFO ? odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 17:56:36,853 14186 INFO ? odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.24s, 15 queries (+15 other) 
2025-05-25 17:56:36,853 14186 INFO ? odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 17:56:37,086 14186 INFO ? odoo.modules.registry: module pos_self_order_preparation_display: creating or updating database tables 
2025-05-25 17:56:37,134 14186 INFO ? odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.28s, 55 queries (+55 other) 
2025-05-25 17:56:37,134 14186 INFO ? odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 17:56:37,296 14186 INFO ? odoo.modules.registry: module pos_self_order_sale: creating or updating database tables 
2025-05-25 17:56:37,318 14186 INFO ? odoo.modules.loading: loading pos_self_order_sale/views/res_config_settings_views.xml 
2025-05-25 17:56:37,322 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:37,342 14186 DEBUG ? odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 17:56:37,380 14186 INFO ? odoo.modules.loading: loading pos_self_order_sale/data/kiosk_sale_team.xml 
2025-05-25 17:56:37,392 14186 INFO ? odoo.modules.loading: Module pos_self_order_sale loaded in 0.26s, 107 queries (+107 other) 
2025-05-25 17:56:37,392 14186 DEBUG ? odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 17:56:37,393 14186 DEBUG ? odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 17:56:37,393 14186 DEBUG ? odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 17:56:37,394 14186 DEBUG ? odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 17:56:37,394 14186 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 17:56:37,394 14186 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 17:56:37,394 14186 INFO ? odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 17:56:37,653 14186 INFO ? odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.26s, 15 queries (+15 other) 
2025-05-25 17:56:37,653 14186 INFO ? odoo.modules.loading: 161 modules loaded in 9.27s, 5634 queries (+5634 extra) 
2025-05-25 17:56:37,768 14186 DEBUG ? odoo.models: column index is in the table auth_totp_device but not in the corresponding object auth_totp.device 
2025-05-25 17:56:37,768 14186 DEBUG ? odoo.models: column key is in the table auth_totp_device but not in the corresponding object auth_totp.device 
2025-05-25 17:56:37,793 14186 DEBUG ? odoo.models: column web is in the table ir_module_module but not in the corresponding object ir.module.module 
2025-05-25 17:56:37,828 14186 DEBUG ? odoo.models: column inverse_rate is in the table res_currency but not in the corresponding object res.currency 
2025-05-25 17:56:37,831 14186 DEBUG ? odoo.models: column password is in the table res_users but not in the corresponding object res.users 
2025-05-25 17:56:37,831 14186 DEBUG ? odoo.models: column totp_secret is in the table res_users but not in the corresponding object res.users 
2025-05-25 17:56:37,832 14186 DEBUG ? odoo.models: column index is in the table res_users_apikeys but not in the corresponding object res.users.apikeys 
2025-05-25 17:56:37,832 14186 DEBUG ? odoo.models: column key is in the table res_users_apikeys but not in the corresponding object res.users.apikeys 
2025-05-25 17:56:37,857 14186 DEBUG ? odoo.models: column purchase_line_id is in the table vendor_delay_report but not in the corresponding object vendor.delay.report 
2025-05-25 17:56:37,889 14186 DEBUG ? odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) will execute at 2025-05-25 17:57:37.865282 
2025-05-25 17:56:37,889 14186 INFO ? odoo.modules.registry: verifying fields for every extended model 
2025-05-25 17:56:38,083 14186 INFO ? odoo.modules.loading: Modules loaded. 
2025-05-25 17:56:38,088 14186 INFO ? odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'groups', 'templates'] 
2025-05-25 17:56:38,090 14186 INFO ? odoo.modules.registry: Registry loaded in 9.728s 
2025-05-25 17:56:38,090 14186 INFO 1005 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-05-25 17:56:38,185 14186 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:56:38] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 6435 1.925 24.474
2025-05-25 17:56:38,504 14186 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-05-25 17:57:20,669 4357 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 17:57:28,425 14186 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 17:57:29,438 14186 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 17:57:29,785 4357 DEBUG ? odoo.service.server: cron1 polling for jobs 
