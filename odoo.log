2025-05-25 17:53:51,015 13061 DEBUG 1005 odoo.api: call pos.session(63,).set_opening_control(0, '') 
2025-05-25 17:53:51,017 13061 WARNING 1005 odoo.http: Record does not exist or has been deleted.
(Record: pos.session(63,), User: 2) 
2025-05-25 17:53:51,017 13061 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:53:51] "POST /web/dataset/call_kw/pos.session/set_opening_control HTTP/1.1" 200 - 3 0.001 0.003
2025-05-25 17:54:02,834 13061 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:54:02] "GET /web/image?model=res.company&id=1&field=logo HTTP/1.1" 304 - 10 0.002 0.004
2025-05-25 17:54:06,662 13061 DEBUG 1005 odoo.api: call pos.session(63,).set_opening_control(0, '') 
2025-05-25 17:54:06,664 13061 WARNING 1005 odoo.http: Record does not exist or has been deleted.
(Record: pos.session(63,), User: 2) 
2025-05-25 17:54:06,664 13061 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:54:06] "POST /web/dataset/call_kw/pos.session/set_opening_control HTTP/1.1" 200 - 3 0.001 0.003
2025-05-25 17:54:20,488 4357 DEBUG ? odoo.service.server: cron0 polling for jobs 
