2025-05-25 18:59:21,626 30832 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-05-25 18:59:21,627 30832 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-05-25 18:59:21,627 30832 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-05-25 18:59:21,627 30832 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-05-25 18:59:21,627 30832 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-05-25 18:59:21,627 30832 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-05-25 18:59:21,627 30832 INFO ? odoo: Odoo version 18.0 
2025-05-25 18:59:21,627 30832 INFO ? odoo: Using configuration file at /odoo/180/odoo.conf 
2025-05-25 18:59:21,627 30832 INFO ? odoo: addons paths: ['/odoo/180/odoo/addons', '/root/.local/share/Odoo/addons/18.0', '/odoo/180/addons', '/odoo/180/custom/Ent180', '/odoo/180/custom/Dev180'] 
2025-05-25 18:59:21,627 30832 INFO ? odoo: database: odoo@default:5433 
2025-05-25 18:59:21,689 30832 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-05-25 18:59:21,693 30832 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltoimage binary at /usr/local/bin/wkhtmltoimage 
2025-05-25 18:59:21,823 30832 DEBUG ? odoo.service.server: Setting signal handlers 
2025-05-25 18:59:21,824 30832 INFO ? odoo.service.server: HTTP service (werkzeug) running on BSD.:10181 
2025-05-25 18:59:21,829 30832 DEBUG 1001 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 8] [Cache default: 10] [Cache assets: 1] [Cache templates: 3] [Cache routing: 1] [Cache groups: 4] 
2025-05-25 18:59:21,830 30832 INFO 1001 odoo.modules.loading: loading 1 modules... 
2025-05-25 18:59:21,831 30832 DEBUG 1001 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:59:21,834 30832 DEBUG 1001 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,835 30832 INFO 1001 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:59:21,871 30832 DEBUG 1001 odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:59:21,872 30832 INFO 1001 odoo.modules.loading: loading 161 modules... 
2025-05-25 18:59:21,872 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:59:21,872 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,872 30832 DEBUG 1001 odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:59:21,873 30832 DEBUG 1001 odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,873 30832 DEBUG 1001 odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:59:21,874 30832 DEBUG 1001 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,874 30832 DEBUG 1001 odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:59:21,880 30832 DEBUG 1001 odoo.modules.loading: Module auth_totp loaded in 0.01s, 0 queries 
2025-05-25 18:59:21,880 30832 DEBUG 1001 odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:59:21,881 30832 DEBUG 1001 odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,881 30832 DEBUG 1001 odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:59:21,970 30832 DEBUG 1001 odoo.modules.loading: Module base_import loaded in 0.09s, 0 queries 
2025-05-25 18:59:21,970 30832 DEBUG 1001 odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:59:21,972 30832 DEBUG 1001 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,972 30832 DEBUG 1001 odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:59:21,973 30832 DEBUG 1001 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,973 30832 DEBUG 1001 odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:59:21,977 30832 DEBUG 1001 odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,978 30832 DEBUG 1001 odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:59:21,979 30832 DEBUG 1001 odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,979 30832 DEBUG 1001 odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:59:21,981 30832 DEBUG 1001 odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,981 30832 DEBUG 1001 odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:59:21,986 30832 DEBUG 1001 odoo.modules.loading: Module resource loaded in 0.01s, 0 queries 
2025-05-25 18:59:21,986 30832 DEBUG 1001 odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:59:21,988 30832 DEBUG 1001 odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,988 30832 DEBUG 1001 odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:59:21,990 30832 DEBUG 1001 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,990 30832 DEBUG 1001 odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:59:21,992 30832 DEBUG 1001 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,992 30832 DEBUG 1001 odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:59:21,993 30832 DEBUG 1001 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,993 30832 DEBUG 1001 odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:59:21,995 30832 DEBUG 1001 odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,995 30832 DEBUG 1001 odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:59:21,996 30832 DEBUG 1001 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,996 30832 DEBUG 1001 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:59:21,996 30832 DEBUG 1001 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:59:21,996 30832 DEBUG 1001 odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:59:22,034 30832 DEBUG 1001 odoo.modules.loading: Module html_editor loaded in 0.04s, 0 queries 
2025-05-25 18:59:22,034 30832 DEBUG 1001 odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:59:22,034 30832 DEBUG 1001 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,034 30832 DEBUG 1001 odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:59:22,035 30832 DEBUG 1001 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,035 30832 DEBUG 1001 odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:59:22,037 30832 DEBUG 1001 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,037 30832 DEBUG 1001 odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:59:22,040 30832 DEBUG 1001 odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,040 30832 DEBUG 1001 odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:59:22,047 30832 DEBUG 1001 odoo.modules.loading: Module web_editor loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,047 30832 DEBUG 1001 odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:59:22,048 30832 DEBUG 1001 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,048 30832 DEBUG 1001 odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:59:22,050 30832 DEBUG 1001 odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,050 30832 DEBUG 1001 odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:59:22,052 30832 DEBUG 1001 odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,052 30832 DEBUG 1001 odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:59:22,053 30832 DEBUG 1001 odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,053 30832 DEBUG 1001 odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:59:22,054 30832 DEBUG 1001 odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,054 30832 DEBUG 1001 odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:59:22,055 30832 DEBUG 1001 odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,055 30832 DEBUG 1001 odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:59:22,056 30832 DEBUG 1001 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,056 30832 DEBUG 1001 odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:59:22,056 30832 DEBUG 1001 odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,057 30832 DEBUG 1001 odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:59:22,057 30832 DEBUG 1001 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,057 30832 DEBUG 1001 odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:59:22,060 30832 DEBUG 1001 odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,060 30832 DEBUG 1001 odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:59:22,062 30832 DEBUG 1001 odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,062 30832 DEBUG 1001 odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:59:22,072 30832 DEBUG 1001 odoo.modules.loading: Module product loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,072 30832 DEBUG 1001 odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:59:22,072 30832 DEBUG 1001 odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,072 30832 DEBUG 1001 odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:59:22,074 30832 DEBUG 1001 odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,074 30832 DEBUG 1001 odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:59:22,075 30832 DEBUG 1001 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,075 30832 DEBUG 1001 odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:59:22,076 30832 DEBUG 1001 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,076 30832 DEBUG 1001 odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:59:22,077 30832 DEBUG 1001 odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,077 30832 DEBUG 1001 odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:59:22,078 30832 DEBUG 1001 odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,078 30832 DEBUG 1001 odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:59:22,082 30832 DEBUG 1001 odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,082 30832 DEBUG 1001 odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:59:22,083 30832 DEBUG 1001 odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,083 30832 DEBUG 1001 odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:59:22,130 30832 DEBUG 1001 odoo.modules.loading: Module sms loaded in 0.05s, 0 queries 
2025-05-25 18:59:22,130 30832 DEBUG 1001 odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:59:22,133 30832 DEBUG 1001 odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,133 30832 DEBUG 1001 odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:59:22,133 30832 DEBUG 1001 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,133 30832 DEBUG 1001 odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:59:22,135 30832 DEBUG 1001 odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,135 30832 DEBUG 1001 odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:59:22,141 30832 DEBUG 1001 odoo.modules.loading: Module payment loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,141 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:59:22,143 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,143 30832 DEBUG 1001 odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:59:22,183 30832 DEBUG 1001 odoo.modules.loading: Module account loaded in 0.04s, 0 queries 
2025-05-25 18:59:22,183 30832 DEBUG 1001 odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:59:22,184 30832 DEBUG 1001 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,184 30832 DEBUG 1001 odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:59:22,193 30832 DEBUG 1001 odoo.modules.loading: Module hr loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,193 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:59:22,194 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,194 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:59:22,196 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,197 30832 DEBUG 1001 odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:59:22,218 30832 DEBUG 1001 odoo.modules.loading: Module stock loaded in 0.02s, 0 queries 
2025-05-25 18:59:22,218 30832 DEBUG 1001 odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:59:22,224 30832 DEBUG 1001 odoo.modules.loading: Module account_accountant loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,224 30832 DEBUG 1001 odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:59:22,226 30832 DEBUG 1001 odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,226 30832 DEBUG 1001 odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:59:22,228 30832 DEBUG 1001 odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,228 30832 DEBUG 1001 odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:59:22,234 30832 DEBUG 1001 odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,234 30832 DEBUG 1001 odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:59:22,235 30832 DEBUG 1001 odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,235 30832 DEBUG 1001 odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:59:22,238 30832 DEBUG 1001 odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,238 30832 DEBUG 1001 odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:59:22,238 30832 DEBUG 1001 odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,238 30832 DEBUG 1001 odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:59:22,239 30832 DEBUG 1001 odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,240 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:59:22,245 30832 DEBUG 1001 odoo.modules.loading: Module hr_expense loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,245 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:59:22,246 30832 DEBUG 1001 odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,246 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:59:22,246 30832 DEBUG 1001 odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,246 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:59:22,247 30832 DEBUG 1001 odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,247 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:59:22,250 30832 DEBUG 1001 odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,250 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:59:22,251 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,251 30832 DEBUG 1001 odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:59:22,251 30832 DEBUG 1001 odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,251 30832 DEBUG 1001 odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:59:22,256 30832 DEBUG 1001 odoo.modules.loading: Module purchase loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,256 30832 DEBUG 1001 odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:59:22,257 30832 DEBUG 1001 odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,257 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:59:22,258 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,258 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:59:22,258 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,258 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:59:22,259 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,259 30832 DEBUG 1001 odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:59:22,264 30832 DEBUG 1001 odoo.modules.loading: Module stock_account loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,265 30832 DEBUG 1001 odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:59:22,268 30832 DEBUG 1001 odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,268 30832 DEBUG 1001 odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:59:22,268 30832 DEBUG 1001 odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,268 30832 DEBUG 1001 odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:59:22,269 30832 DEBUG 1001 odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,269 30832 DEBUG 1001 odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:59:22,271 30832 DEBUG 1001 odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,271 30832 DEBUG 1001 odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:59:22,271 30832 DEBUG 1001 odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,271 30832 DEBUG 1001 odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:59:22,273 30832 DEBUG 1001 odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,273 30832 DEBUG 1001 odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:59:22,276 30832 DEBUG 1001 odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,276 30832 DEBUG 1001 odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:59:22,277 30832 DEBUG 1001 odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,277 30832 DEBUG 1001 odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:59:22,278 30832 DEBUG 1001 odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,278 30832 DEBUG 1001 odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:59:22,282 30832 DEBUG 1001 odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,282 30832 DEBUG 1001 odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:59:22,282 30832 DEBUG 1001 odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,282 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:59:22,283 30832 DEBUG 1001 odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,283 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:59:22,284 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,284 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:59:22,284 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,284 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:59:22,285 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,285 30832 DEBUG 1001 odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:59:22,299 30832 DEBUG 1001 odoo.modules.loading: Module point_of_sale loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,300 30832 DEBUG 1001 odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:59:22,300 30832 DEBUG 1001 odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,300 30832 DEBUG 1001 odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:59:22,305 30832 DEBUG 1001 odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,305 30832 DEBUG 1001 odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Module sale loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,314 30832 DEBUG 1001 odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:59:22,315 30832 DEBUG 1001 odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,315 30832 DEBUG 1001 odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:59:22,316 30832 DEBUG 1001 odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,316 30832 DEBUG 1001 odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:59:22,317 30832 DEBUG 1001 odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,317 30832 DEBUG 1001 odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:59:22,319 30832 DEBUG 1001 odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,319 30832 DEBUG 1001 odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:59:22,319 30832 DEBUG 1001 odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,319 30832 DEBUG 1001 odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:59:22,333 30832 DEBUG 1001 odoo.modules.loading: Module account_reports loaded in 0.01s, 0 queries 
2025-05-25 18:59:22,333 30832 DEBUG 1001 odoo.modules.loading: Loading module hr_expense_extract (107/161) 
2025-05-25 18:59:22,334 30832 DEBUG 1001 odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,335 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_avatax (108/161) 
2025-05-25 18:59:22,335 30832 DEBUG 1001 odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,335 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_barcodelookup (109/161) 
2025-05-25 18:59:22,335 30832 DEBUG 1001 odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,335 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_enterprise (110/161) 
2025-05-25 18:59:22,336 30832 DEBUG 1001 odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,336 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_epson_printer (111/161) 
2025-05-25 18:59:22,336 30832 DEBUG 1001 odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,336 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_hr (112/161) 
2025-05-25 18:59:22,338 30832 DEBUG 1001 odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,338 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_multi_currency_payment (113/161) 
2025-05-25 18:59:22,404 30832 ERROR 1001 odoo.tests.common: Importing test framework, avoid importing from business modules and when not running in test mode 
Stack (most recent call last):
  File "/usr/lib/python3.12/runpy.py", line 198, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.12/runpy.py", line 88, in _run_code
    exec(code, run_globals)
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/__main__.py", line 71, in <module>
    cli.main()
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/../debugpy/server/cli.py", line 501, in main
    run()
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/../debugpy/server/cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
  File "/root/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
  File "/odoo/180/odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "/odoo/180/odoo/cli/command.py", line 66, in main
    o.run(args)
  File "/odoo/180/odoo/cli/server.py", line 182, in run
    main(args)
  File "/odoo/180/odoo/cli/server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "/odoo/180/odoo/service/server.py", line 1425, in start
    rc = server.run(preload, stop)
  File "/odoo/180/odoo/service/server.py", line 590, in run
    rc = preload_registries(preload)
  File "/odoo/180/odoo/service/server.py", line 1329, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "/odoo/180/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "/odoo/180/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/odoo/180/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/odoo/180/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/odoo/180/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/odoo/180/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/odoo/180/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/__init__.py", line 3, in <module>
    from . import tests
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/tests/__init__.py", line 1, in <module>
    from . import test_pos_multi_currency_payment
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/custom/Dev180/pos_multi_currency_payment/tests/test_pos_multi_currency_payment.py", line 2, in <module>
    from odoo.tests import tagged
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/odoo/tests/__init__.py", line 8, in <module>
    from . import common
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/odoo/180/odoo/tests/common.py", line 100, in <module>
    _logger.error(
2025-05-25 18:59:22,482 30832 DEBUG 1001 odoo.modules.loading: Module pos_multi_currency_payment loaded in 0.14s, 0 queries 
2025-05-25 18:59:22,482 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 18:59:22,486 30832 DEBUG 1001 odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,486 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 18:59:22,488 30832 DEBUG 1001 odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,489 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 18:59:22,491 30832 DEBUG 1001 odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,491 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 18:59:22,491 30832 DEBUG 1001 odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,492 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 18:59:22,492 30832 DEBUG 1001 odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,492 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 18:59:22,492 30832 DEBUG 1001 odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,493 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 18:59:22,493 30832 DEBUG 1001 odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,493 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 18:59:22,494 30832 DEBUG 1001 odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,494 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 18:59:22,498 30832 DEBUG 1001 odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,498 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 18:59:22,502 30832 DEBUG 1001 odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,502 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 18:59:22,502 30832 DEBUG 1001 odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,502 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 18:59:22,507 30832 DEBUG 1001 odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,507 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 18:59:22,507 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,507 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 18:59:22,508 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,508 30832 DEBUG 1001 odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 18:59:22,511 30832 DEBUG 1001 odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,511 30832 DEBUG 1001 odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 18:59:22,512 30832 DEBUG 1001 odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,512 30832 DEBUG 1001 odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 18:59:22,513 30832 DEBUG 1001 odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,513 30832 DEBUG 1001 odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 18:59:22,515 30832 DEBUG 1001 odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,515 30832 DEBUG 1001 odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 18:59:22,517 30832 DEBUG 1001 odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,517 30832 DEBUG 1001 odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 18:59:22,518 30832 DEBUG 1001 odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,518 30832 DEBUG 1001 odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 18:59:22,519 30832 DEBUG 1001 odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,519 30832 DEBUG 1001 odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,520 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 18:59:22,521 30832 DEBUG 1001 odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,521 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 18:59:22,521 30832 DEBUG 1001 odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,521 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 18:59:22,522 30832 DEBUG 1001 odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,522 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 18:59:22,524 30832 DEBUG 1001 odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,524 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 18:59:22,528 30832 DEBUG 1001 odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,528 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 18:59:22,529 30832 DEBUG 1001 odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,530 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 18:59:22,533 30832 DEBUG 1001 odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,533 30832 DEBUG 1001 odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 18:59:22,534 30832 DEBUG 1001 odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,534 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 18:59:22,534 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,534 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 18:59:22,535 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,535 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 18:59:22,535 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,535 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 18:59:22,536 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,536 30832 DEBUG 1001 odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 18:59:22,537 30832 DEBUG 1001 odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,537 30832 DEBUG 1001 odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 18:59:22,537 30832 DEBUG 1001 odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,537 30832 DEBUG 1001 odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 18:59:22,540 30832 DEBUG 1001 odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,540 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 18:59:22,542 30832 DEBUG 1001 odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,542 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 18:59:22,542 30832 DEBUG 1001 odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,542 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 18:59:22,543 30832 DEBUG 1001 odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,543 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 18:59:22,543 30832 DEBUG 1001 odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,544 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 18:59:22,544 30832 DEBUG 1001 odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,544 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 18:59:22,545 30832 DEBUG 1001 odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,545 30832 DEBUG 1001 odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 18:59:22,546 30832 DEBUG 1001 odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,546 30832 DEBUG 1001 odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 18:59:22,546 30832 DEBUG 1001 odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,546 30832 DEBUG 1001 odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 18:59:22,546 30832 DEBUG 1001 odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:22,546 30832 INFO 1001 odoo.modules.loading: 161 modules loaded in 0.67s, 0 queries (+0 extra) 
2025-05-25 18:59:22,663 30832 INFO 1001 odoo.modules.loading: Modules loaded. 
2025-05-25 18:59:22,667 30832 INFO 1001 odoo.modules.registry: Registry loaded in 0.842s 
2025-05-25 18:59:22,669 30832 DEBUG 1001 odoo.service.server: cron0 started! 
2025-05-25 18:59:22,669 30832 DEBUG 1001 odoo.service.server: cron1 started! 
2025-05-25 18:59:27,616 30832 DEBUG ? odoo.http: HTTP sessions stored in: /root/.local/share/Odoo/sessions 
2025-05-25 18:59:27,621 30832 DEBUG ? odoo.modules.registry: Multiprocess load registry signaling: [Registry: 68] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:59:27,622 30832 INFO ? odoo.modules.loading: loading 1 modules... 
2025-05-25 18:59:27,622 30832 DEBUG ? odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:59:27,625 30832 DEBUG ? odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,625 30832 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:59:27,632 30832 DEBUG ? odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:59:27,632 30832 INFO ? odoo.modules.loading: loading 161 modules... 
2025-05-25 18:59:27,632 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:59:27,632 30832 DEBUG ? odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,632 30832 DEBUG ? odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:59:27,633 30832 DEBUG ? odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,633 30832 DEBUG ? odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:59:27,633 30832 DEBUG ? odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,634 30832 DEBUG ? odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:59:27,634 30832 DEBUG ? odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,634 30832 DEBUG ? odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:59:27,634 30832 DEBUG ? odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,634 30832 DEBUG ? odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,635 30832 DEBUG ? odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,636 30832 DEBUG ? odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:59:27,637 30832 DEBUG ? odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,637 30832 DEBUG ? odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:59:27,637 30832 DEBUG ? odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,637 30832 DEBUG ? odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:59:27,638 30832 DEBUG ? odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,638 30832 DEBUG ? odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,639 30832 DEBUG ? odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:59:27,640 30832 DEBUG ? odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,640 30832 DEBUG ? odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:59:27,642 30832 DEBUG ? odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,642 30832 DEBUG ? odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:59:27,644 30832 DEBUG ? odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,644 30832 DEBUG ? odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:59:27,644 30832 DEBUG ? odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,644 30832 DEBUG ? odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,645 30832 DEBUG ? odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,646 30832 DEBUG ? odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:59:27,647 30832 DEBUG ? odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,648 30832 DEBUG ? odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:59:27,648 30832 DEBUG ? odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,648 30832 DEBUG ? odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,649 30832 DEBUG ? odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,650 30832 DEBUG ? odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:59:27,651 30832 DEBUG ? odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,651 30832 DEBUG ? odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:59:27,651 30832 DEBUG ? odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,651 30832 DEBUG ? odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,653 30832 DEBUG ? odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:59:27,654 30832 DEBUG ? odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,654 30832 DEBUG ? odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:59:27,654 30832 DEBUG ? odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,654 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:59:27,654 30832 DEBUG ? odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,655 30832 DEBUG ? odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:59:27,656 30832 DEBUG ? odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,656 30832 DEBUG ? odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:59:27,656 30832 DEBUG ? odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,657 30832 DEBUG ? odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,659 30832 DEBUG ? odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:59:27,661 30832 DEBUG ? odoo.modules.loading: Module stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,662 30832 DEBUG ? odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:59:27,662 30832 DEBUG ? odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,662 30832 DEBUG ? odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:59:27,662 30832 DEBUG ? odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,662 30832 DEBUG ? odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,663 30832 DEBUG ? odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,664 30832 DEBUG ? odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,665 30832 DEBUG ? odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,666 30832 DEBUG ? odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,667 30832 DEBUG ? odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,668 30832 DEBUG ? odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,669 30832 DEBUG ? odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,670 30832 DEBUG ? odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,671 30832 DEBUG ? odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:59:27,673 30832 DEBUG ? odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,673 30832 DEBUG ? odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:59:27,673 30832 DEBUG ? odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,673 30832 DEBUG ? odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:59:27,674 30832 DEBUG ? odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,674 30832 DEBUG ? odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:59:27,675 30832 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,676 30832 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:59:27,676 30832 DEBUG ? odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,676 30832 DEBUG ? odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:59:27,676 30832 DEBUG ? odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,676 30832 DEBUG ? odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:59:27,677 30832 DEBUG ? odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,677 30832 DEBUG ? odoo.modules.loading: Loading module bs_pos_mcurrancy (107/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module bs_pos_mcurrancy loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module hr_expense_extract (108/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module pos_avatax (109/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module pos_barcodelookup (110/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module pos_enterprise (111/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module pos_epson_printer (112/161) 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,679 30832 DEBUG ? odoo.modules.loading: Loading module pos_hr (113/161) 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,680 30832 DEBUG ? odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,681 30832 DEBUG ? odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,682 30832 DEBUG ? odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,683 30832 DEBUG ? odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,684 30832 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,685 30832 DEBUG ? odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 18:59:27,686 30832 DEBUG ? odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,686 30832 DEBUG ? odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 18:59:27,686 30832 DEBUG ? odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,686 30832 DEBUG ? odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,687 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,688 30832 DEBUG ? odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,689 30832 DEBUG ? odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 18:59:27,690 30832 DEBUG ? odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:27,690 30832 INFO ? odoo.modules.loading: 161 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-05-25 18:59:27,860 30832 INFO ? odoo.modules.loading: Modules loaded. 
2025-05-25 18:59:27,863 30832 INFO ? odoo.modules.registry: Registry loaded in 0.246s 
2025-05-25 18:59:27,864 30832 INFO 1005 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-05-25 18:59:28,103 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "GET /odoo/apps HTTP/1.1" 200 - 129 0.038 0.449
2025-05-25 18:59:28,376 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 4 0.001 0.003
2025-05-25 18:59:28,431 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 17 0.006 0.013
2025-05-25 18:59:28,720 30832 DEBUG ? odoo.modules.module: module __init__.py: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,721 30832 DEBUG ? odoo.modules.module: module __pycache__: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,748 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.001 0.012
2025-05-25 18:59:28,753 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.009 0.012
2025-05-25 18:59:28,777 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "POST /mail/data HTTP/1.1" 200 - 36 0.022 0.021
2025-05-25 18:59:28,844 30832 DEBUG ? odoo.modules.module: module website_twitter: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,857 30832 DEBUG ? odoo.modules.module: module LICENSE: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,893 30832 DEBUG ? odoo.modules.module: module CONTRIBUTING.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,899 30832 DEBUG ? odoo.modules.module: module README.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,950 30832 DEBUG ? odoo.modules.module: module COPYRIGHT: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:28,989 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:28] "POST /web/action/load HTTP/1.1" 200 - 12 0.004 0.268
2025-05-25 18:59:29,070 30832 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-05-25 18:59:29,307 30832 DEBUG 1005 odoo.api: call ir.module.module().get_views(options={'action_id': 39, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'kanban'], [False, 'list'], [False, 'form'], [95, 'search']]) 
2025-05-25 18:59:29,343 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:29] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.013 0.024
2025-05-25 18:59:29,552 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('module_type', category_domain=[], enable_counters=False, expand=True, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:59:29,553 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:59:29,660 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:59:29,661 30832 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=[['application', '=', True]], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:59:29,673 30832 DEBUG 1005 odoo.modules.module: module wbl_pos_multi_currency: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-05-25 18:59:29,683 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.003 0.022
2025-05-25 18:59:29,689 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 53 0.018 0.013
2025-05-25 18:59:29,807 30832 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:59:29,901 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:29] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 63 0.018 0.079
2025-05-25 18:59:30,558 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:30] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:59:31,753 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=[]) 
2025-05-25 18:59:31,768 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:31] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 51 0.009 0.008
2025-05-25 18:59:32,058 30832 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:59:32,069 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:32] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.003 0.011
2025-05-25 18:59:32,423 30832 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:59:32,427 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:32] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 8 0.002 0.005
2025-05-25 18:59:35,269 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=['|', '|', ['summary', 'ilike', 'multi'], ['shortdesc', 'ilike', 'multi'], ['name', 'ilike', 'multi']]) 
2025-05-25 18:59:35,297 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:35] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 51 0.019 0.010
2025-05-25 18:59:35,615 30832 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=['|', '|', ['summary', 'ilike', 'multi'], ['shortdesc', 'ilike', 'multi'], ['name', 'ilike', 'multi']], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:59:35,618 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:35] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.002 0.003
2025-05-25 18:59:35,875 30832 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:59:35,881 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:35] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 8 0.002 0.005
2025-05-25 18:59:38,431 30832 DEBUG 1005 odoo.api: call ir.module.module(1299,).button_uninstall_wizard() 
2025-05-25 18:59:38,432 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:59:38,433 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:38] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.001 0.003
2025-05-25 18:59:38,744 30832 DEBUG 1005 odoo.api: call base.module.uninstall().get_views(options={'action_id': False, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': False, 'toolbar': False}, views=[[False, 'form']]) 
2025-05-25 18:59:38,754 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:38] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.004 0.008
2025-05-25 18:59:39,006 30832 DEBUG 1005 odoo.api: call base.module.uninstall().onchange({}, [], {'show_all': {}, 'module_ids': {'fields': {'state': {}, 'icon': {}, 'shortdesc': {}, 'name': {}}, 'limit': 40, 'order': ''}, 'model_ids': {'fields': {'name': {}, 'count': {}}, 'limit': 40, 'order': ''}}) 
2025-05-25 18:59:39,013 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:39] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 9 0.004 0.005
2025-05-25 18:59:40,072 30832 DEBUG 1005 odoo.api: call base.module.uninstall().web_save({'show_all': False}, specification={'show_all': {}, 'module_ids': {'fields': {'state': {}, 'icon': {}, 'shortdesc': {}, 'name': {}}, 'limit': 40, 'order': ''}, 'model_ids': {'fields': {'name': {}, 'count': {}}, 'limit': 40, 'order': ''}}) 
2025-05-25 18:59:40,086 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:40] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 11 0.004 0.012
2025-05-25 18:59:40,406 30832 DEBUG 1005 odoo.api: call base.module.uninstall(9,).action_uninstall() 
2025-05-25 18:59:40,408 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:59:40,408 30832 INFO 1005 odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-05-25 18:59:40,408 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['BS POS Multi Currency'] to user admin #2 via 127.0.0.1 
2025-05-25 18:59:40,415 30832 DEBUG 1005 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 68] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:59:40,418 30832 INFO 1005 odoo.modules.loading: loading 1 modules... 
2025-05-25 18:59:40,418 30832 DEBUG 1005 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:59:40,421 30832 DEBUG 1005 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:59:40,422 30832 INFO 1005 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:59:40,433 30832 INFO 1005 odoo.modules.loading: updating modules list 
2025-05-25 18:59:40,435 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Updating graph with 160 more modules 
2025-05-25 18:59:41,137 30832 INFO 1005 odoo.modules.loading: loading 161 modules... 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us (2/161) 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Loading module uom (3/161) 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,137 30832 DEBUG 1005 odoo.modules.loading: Loading module web (4/161) 
2025-05-25 18:59:41,138 30832 DEBUG 1005 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,138 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp (5/161) 
2025-05-25 18:59:41,138 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,139 30832 DEBUG 1005 odoo.modules.loading: Loading module barcodes (6/161) 
2025-05-25 18:59:41,139 30832 DEBUG 1005 odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,139 30832 DEBUG 1005 odoo.modules.loading: Loading module base_import (7/161) 
2025-05-25 18:59:41,139 30832 DEBUG 1005 odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,139 30832 DEBUG 1005 odoo.modules.loading: Loading module base_import_module (8/161) 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Loading module base_setup (9/161) 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Loading module bus (10/161) 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Loading module http_routing (11/161) 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Loading module onboarding (12/161) 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,140 30832 DEBUG 1005 odoo.modules.loading: Loading module resource (13/161) 
2025-05-25 18:59:41,141 30832 DEBUG 1005 odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,141 30832 DEBUG 1005 odoo.modules.loading: Loading module utm (14/161) 
2025-05-25 18:59:41,141 30832 DEBUG 1005 odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,141 30832 DEBUG 1005 odoo.modules.loading: Loading module web_cohort (15/161) 
2025-05-25 18:59:41,142 30832 DEBUG 1005 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,142 30832 DEBUG 1005 odoo.modules.loading: Loading module web_gantt (16/161) 
2025-05-25 18:59:41,142 30832 DEBUG 1005 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,142 30832 DEBUG 1005 odoo.modules.loading: Loading module web_grid (17/161) 
2025-05-25 18:59:41,143 30832 DEBUG 1005 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,143 30832 DEBUG 1005 odoo.modules.loading: Loading module web_hierarchy (18/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Loading module web_tour (19/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Loading module html_editor (21/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Loading module iap (22/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Loading module web_enterprise (23/161) 
2025-05-25 18:59:41,144 30832 DEBUG 1005 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,145 30832 DEBUG 1005 odoo.modules.loading: Loading module web_map (24/161) 
2025-05-25 18:59:41,145 30832 DEBUG 1005 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,145 30832 DEBUG 1005 odoo.modules.loading: Loading module mail (25/161) 
2025-05-25 18:59:41,148 30832 DEBUG 1005 odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,148 30832 DEBUG 1005 odoo.modules.loading: Loading module web_editor (26/161) 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Loading module web_mobile (27/161) 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Loading module analytic (28/161) 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_signup (29/161) 
2025-05-25 18:59:41,150 30832 DEBUG 1005 odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_mail (30/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module base_install_request (31/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module google_gmail (32/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module iap_mail (33/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_bot (34/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_enterprise (35/161) 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,151 30832 DEBUG 1005 odoo.modules.loading: Loading module phone_validation (36/161) 
2025-05-25 18:59:41,152 30832 DEBUG 1005 odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,153 30832 DEBUG 1005 odoo.modules.loading: Loading module privacy_lookup (37/161) 
2025-05-25 18:59:41,153 30832 DEBUG 1005 odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,153 30832 DEBUG 1005 odoo.modules.loading: Loading module product (38/161) 
2025-05-25 18:59:41,153 30832 DEBUG 1005 odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module resource_mail (39/161) 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module sales_team (40/161) 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module web_unsplash (41/161) 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module iap_extract (42/161) 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_mobile (43/161) 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,154 30832 DEBUG 1005 odoo.modules.loading: Loading module partner_autocomplete (44/161) 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Loading module portal (45/161) 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Loading module product_barcodelookup (46/161) 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,155 30832 DEBUG 1005 odoo.modules.loading: Loading module sms (47/161) 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail (48/161) 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_portal (49/161) 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,157 30832 DEBUG 1005 odoo.modules.loading: Loading module digest (50/161) 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Loading module payment (51/161) 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet (52/161) 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,158 30832 DEBUG 1005 odoo.modules.loading: Loading module account (53/161) 
2025-05-25 18:59:41,160 30832 DEBUG 1005 odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,160 30832 DEBUG 1005 odoo.modules.loading: Loading module digest_enterprise (54/161) 
2025-05-25 18:59:41,160 30832 DEBUG 1005 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,160 30832 DEBUG 1005 odoo.modules.loading: Loading module hr (55/161) 
2025-05-25 18:59:41,163 30832 DEBUG 1005 odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,163 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard (56/161) 
2025-05-25 18:59:41,163 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,163 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_edition (57/161) 
2025-05-25 18:59:41,164 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,164 30832 DEBUG 1005 odoo.modules.loading: Loading module stock (58/161) 
2025-05-25 18:59:41,165 30832 DEBUG 1005 odoo.modules.loading: Module stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,165 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant (59/161) 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Loading module account_batch_payment (60/161) 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Loading module account_check_printing (61/161) 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Loading module account_edi_ubl_cii (62/161) 
2025-05-25 18:59:41,166 30832 DEBUG 1005 odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Loading module account_external_tax (63/161) 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Loading module account_payment (64/161) 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Loading module analytic_enterprise (65/161) 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Loading module currency_rate_live (66/161) 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,167 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense (67/161) 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_gantt (68/161) 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_mobile (69/161) 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_org_chart (70/161) 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_skills (71/161) 
2025-05-25 18:59:41,168 30832 DEBUG 1005 odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_account (72/161) 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_bot_hr (73/161) 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase (74/161) 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account (75/161) 
2025-05-25 18:59:41,169 30832 DEBUG 1005 odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_account (76/161) 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/161) 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/161) 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_account (79/161) 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,170 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_barcode (80/161) 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_enterprise (81/161) 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_sms (82/161) 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_batch_payment (83/161) 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_check_printing (84/161) 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,171 30832 DEBUG 1005 odoo.modules.loading: Loading module account_auto_transfer (85/161) 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax (86/161) 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import (87/161) 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Loading module account_base_import (88/161) 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,172 30832 DEBUG 1005 odoo.modules.loading: Loading module account_online_synchronization (89/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module accountant (90/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_predict_product (91/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_1099 (92/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_check_printing (93/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_payment_nacha (94/161) 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,173 30832 DEBUG 1005 odoo.modules.loading: Loading module point_of_sale (95/161) 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/161) 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase_stock (97/161) 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,175 30832 DEBUG 1005 odoo.modules.loading: Loading module sale (98/161) 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/161) 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/161) 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_accountant (101/161) 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,176 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_camt (102/161) 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_csv (103/161) 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/161) 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Loading module account_extract (105/161) 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,177 30832 DEBUG 1005 odoo.modules.loading: Loading module account_reports (106/161) 
2025-05-25 18:59:41,178 30832 DEBUG 1005 odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,178 30832 DEBUG 1005 odoo.modules.loading: Loading module bs_pos_mcurrancy (107/161) 
2025-05-25 18:59:41,178 30832 DEBUG 1005 odoo.modules.loading: Module bs_pos_mcurrancy loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,178 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_extract (108/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_avatax (109/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_barcodelookup (110/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_enterprise (111/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_epson_printer (112/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr (113/161) 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,179 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment (114/161) 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_preparation_display (115/161) 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant (116/161) 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_sms (117/161) 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,180 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_account_accountant (118/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_async_emails (119/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_edi_ubl (120/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_external_tax (121/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_management (122/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase (123/161) 
2025-05-25 18:59:41,181 30832 DEBUG 1005 odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_sms (124/161) 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_stock (125/161) 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (126/161) 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_sale (127/161) 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,182 30832 DEBUG 1005 odoo.modules.loading: Loading module account_asset (128/161) 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_sale (129/161) 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_extract (130/161) 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Loading module account_disallowed_expenses (131/161) 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Loading module account_followup (132/161) 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,183 30832 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract (133/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module account_reports_cash_basis (134/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_reports (135/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_account_reports (136/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_mobile (137/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_preparation_display (138/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_restaurant (139/161) 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,184 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant_preparation_display (140/161) 
2025-05-25 18:59:41,185 30832 DEBUG 1005 odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,185 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_sale (141/161) 
2025-05-25 18:59:41,185 30832 DEBUG 1005 odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,185 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order (142/161) 
2025-05-25 18:59:41,185 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_expense (143/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_pdf_quote_builder (144/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase_stock (145/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (146/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (147/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (148/161) 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,186 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_sale_management (149/161) 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_stock (150/161) 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract_purchase (151/161) 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Loading module account_loans (152/161) 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order (153/161) 
2025-05-25 18:59:41,187 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_order_tracking_display (154/161) 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_epson_printer (155/161) 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_preparation_display (156/161) 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_sale (157/161) 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_settle_due (158/161) 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,188 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account_followup (159/161) 
2025-05-25 18:59:41,189 30832 DEBUG 1005 odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,189 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (160/161) 
2025-05-25 18:59:41,189 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,189 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (161/161) 
2025-05-25 18:59:41,189 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,189 30832 INFO 1005 odoo.modules.loading: 161 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-05-25 18:59:41,377 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['BS POS Multi Currency'] to user __system__ #1 via 127.0.0.1 
2025-05-25 18:59:41,379 30832 INFO 1005 odoo.addons.base.models.ir_model: Deleting ir.ui.view(2000, 1999, 1993) 
2025-05-25 18:59:41,381 30832 DEBUG 1005 odoo.modules.registry: Invalidating default model caches from unlink /odoo/180/odoo/addons/base/models/ir_model.py:2299 
2025-05-25 18:59:41,382 30832 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from unlink /odoo/180/odoo/addons/base/models/ir_ui_view.py:556 
2025-05-25 18:59:41,400 30832 DEBUG 1005 odoo.modules.registry: Invalidating default model caches from unlink /odoo/180/odoo/addons/base/models/ir_model.py:2299 
2025-05-25 18:59:41,403 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [80058, 80406, 80664] 
2025-05-25 18:59:41,403 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [2000, 1999, 1993] 
2025-05-25 18:59:41,403 30832 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from unlink /odoo/180/odoo/addons/base/models/ir_ui_view.py:556 
2025-05-25 18:59:41,404 30832 INFO 1005 odoo.addons.base.models.ir_model: Deleting ir.model.fields(11328, 11327, 11322, 11321, 11320, 11319, 11318, 11317, 11303, 11302, 11301, 11300) 
2025-05-25 18:59:41,405 30832 DEBUG 1005 odoo.modules.registry: Invalidating default model caches from unlink /odoo/180/odoo/addons/base/models/ir_model.py:2299 
2025-05-25 18:59:41,517 30832 DEBUG 1005 odoo.modules.registry: Invalidating default model caches from unlink /odoo/180/odoo/addons/base/models/ir_model.py:2299 
2025-05-25 18:59:41,520 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [80365, 80366, 80367, 79972, 80368, 79973, 79970, 79971, 80622, 80623, 80369, 80370] 
2025-05-25 18:59:41,520 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11328, 11327, 11322, 11321, 11320, 11319, 11318, 11317, 11303, 11302, 11301, 11300] 
2025-05-25 18:59:41,523 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.model.relation records with IDs: [230] 
2025-05-25 18:59:41,524 30832 INFO 1005 odoo.addons.base.models.ir_model: Dropped table pos_config_res_currency_rel 
2025-05-25 18:59:41,525 30832 INFO 1005 odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-05-25 18:59:41,525 30832 DEBUG 1005 odoo.modules.registry: Invalidating default model caches from unlink /odoo/180/odoo/addons/base/models/ir_model.py:2299 
2025-05-25 18:59:41,526 30832 INFO 1005 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [80664, 80623, 80622, 80611, 80406, 80370, 80369, 80368, 80367, 80366, 80365, 80058, 80011, 79973, 79972, 79971, 79970, 79967, 79966, 79965] 
2025-05-25 18:59:41,533 30832 INFO 1005 odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-05-25 18:59:41,535 30832 DEBUG 1005 odoo.modules.registry: Multiprocess load registry signaling: [Registry: 68] [Cache default: 9] [Cache assets: 1] [Cache templates: 4] [Cache routing: 1] [Cache groups: 5] 
2025-05-25 18:59:41,537 30832 INFO 1005 odoo.modules.loading: loading 1 modules... 
2025-05-25 18:59:41,537 30832 DEBUG 1005 odoo.modules.loading: Loading module base (1/1) 
2025-05-25 18:59:41,541 30832 DEBUG 1005 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-05-25 18:59:41,541 30832 INFO 1005 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-05-25 18:59:41,553 30832 INFO 1005 odoo.modules.loading: updating modules list 
2025-05-25 18:59:41,554 30832 INFO 1005 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-05-25 18:59:42,171 30832 DEBUG 1005 odoo.modules.loading: Updating graph with 159 more modules 
2025-05-25 18:59:42,172 30832 INFO 1005 odoo.modules.loading: loading 160 modules... 
2025-05-25 18:59:42,172 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us (2/160) 
2025-05-25 18:59:42,172 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,172 30832 DEBUG 1005 odoo.modules.loading: Loading module uom (3/160) 
2025-05-25 18:59:42,172 30832 DEBUG 1005 odoo.modules.loading: Module uom loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,172 30832 DEBUG 1005 odoo.modules.loading: Loading module web (4/160) 
2025-05-25 18:59:42,173 30832 DEBUG 1005 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,173 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp (5/160) 
2025-05-25 18:59:42,173 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,173 30832 DEBUG 1005 odoo.modules.loading: Loading module barcodes (6/160) 
2025-05-25 18:59:42,173 30832 DEBUG 1005 odoo.modules.loading: Module barcodes loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,174 30832 DEBUG 1005 odoo.modules.loading: Loading module base_import (7/160) 
2025-05-25 18:59:42,174 30832 DEBUG 1005 odoo.modules.loading: Module base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,174 30832 DEBUG 1005 odoo.modules.loading: Loading module base_import_module (8/160) 
2025-05-25 18:59:42,174 30832 DEBUG 1005 odoo.modules.loading: Module base_import_module loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Loading module base_setup (9/160) 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Module base_setup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Loading module bus (10/160) 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Module bus loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Loading module http_routing (11/160) 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Module http_routing loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Loading module onboarding (12/160) 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Module onboarding loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,175 30832 DEBUG 1005 odoo.modules.loading: Loading module resource (13/160) 
2025-05-25 18:59:42,176 30832 DEBUG 1005 odoo.modules.loading: Module resource loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,176 30832 DEBUG 1005 odoo.modules.loading: Loading module utm (14/160) 
2025-05-25 18:59:42,176 30832 DEBUG 1005 odoo.modules.loading: Module utm loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,176 30832 DEBUG 1005 odoo.modules.loading: Loading module web_cohort (15/160) 
2025-05-25 18:59:42,177 30832 DEBUG 1005 odoo.modules.loading: Module web_cohort loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,177 30832 DEBUG 1005 odoo.modules.loading: Loading module web_gantt (16/160) 
2025-05-25 18:59:42,177 30832 DEBUG 1005 odoo.modules.loading: Module web_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,177 30832 DEBUG 1005 odoo.modules.loading: Loading module web_grid (17/160) 
2025-05-25 18:59:42,178 30832 DEBUG 1005 odoo.modules.loading: Module web_grid loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,178 30832 DEBUG 1005 odoo.modules.loading: Loading module web_hierarchy (18/160) 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Module web_hierarchy loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Loading module web_tour (19/160) 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Loading module barcodes_gs1_nomenclature (20/160) 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Module barcodes_gs1_nomenclature loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Loading module html_editor (21/160) 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Module html_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,179 30832 DEBUG 1005 odoo.modules.loading: Loading module iap (22/160) 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Module iap loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Loading module web_enterprise (23/160) 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Module web_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Loading module web_map (24/160) 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Module web_map loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,180 30832 DEBUG 1005 odoo.modules.loading: Loading module mail (25/160) 
2025-05-25 18:59:42,183 30832 DEBUG 1005 odoo.modules.loading: Module mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,183 30832 DEBUG 1005 odoo.modules.loading: Loading module web_editor (26/160) 
2025-05-25 18:59:42,185 30832 DEBUG 1005 odoo.modules.loading: Module web_editor loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,185 30832 DEBUG 1005 odoo.modules.loading: Loading module web_mobile (27/160) 
2025-05-25 18:59:42,185 30832 DEBUG 1005 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,185 30832 DEBUG 1005 odoo.modules.loading: Loading module analytic (28/160) 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Module analytic loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_signup (29/160) 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Module auth_signup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_mail (30/160) 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Loading module base_install_request (31/160) 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Module base_install_request loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Loading module google_gmail (32/160) 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Module google_gmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,186 30832 DEBUG 1005 odoo.modules.loading: Loading module iap_mail (33/160) 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_bot (34/160) 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Module mail_bot loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_enterprise (35/160) 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,187 30832 DEBUG 1005 odoo.modules.loading: Loading module phone_validation (36/160) 
2025-05-25 18:59:42,188 30832 DEBUG 1005 odoo.modules.loading: Module phone_validation loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,188 30832 DEBUG 1005 odoo.modules.loading: Loading module privacy_lookup (37/160) 
2025-05-25 18:59:42,188 30832 DEBUG 1005 odoo.modules.loading: Module privacy_lookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,188 30832 DEBUG 1005 odoo.modules.loading: Loading module product (38/160) 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Module product loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Loading module resource_mail (39/160) 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Module resource_mail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Loading module sales_team (40/160) 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Module sales_team loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Loading module web_unsplash (41/160) 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Module web_unsplash loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,189 30832 DEBUG 1005 odoo.modules.loading: Loading module iap_extract (42/160) 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_mobile (43/160) 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Module mail_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Loading module partner_autocomplete (44/160) 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Module partner_autocomplete loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,190 30832 DEBUG 1005 odoo.modules.loading: Loading module portal (45/160) 
2025-05-25 18:59:42,191 30832 DEBUG 1005 odoo.modules.loading: Module portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,191 30832 DEBUG 1005 odoo.modules.loading: Loading module product_barcodelookup (46/160) 
2025-05-25 18:59:42,191 30832 DEBUG 1005 odoo.modules.loading: Module product_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,191 30832 DEBUG 1005 odoo.modules.loading: Loading module sms (47/160) 
2025-05-25 18:59:42,192 30832 DEBUG 1005 odoo.modules.loading: Module sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,192 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail (48/160) 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Module snailmail loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Loading module auth_totp_portal (49/160) 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Loading module digest (50/160) 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Module digest loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Loading module payment (51/160) 
2025-05-25 18:59:42,193 30832 DEBUG 1005 odoo.modules.loading: Module payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,194 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet (52/160) 
2025-05-25 18:59:42,194 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,194 30832 DEBUG 1005 odoo.modules.loading: Loading module account (53/160) 
2025-05-25 18:59:42,195 30832 DEBUG 1005 odoo.modules.loading: Module account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,195 30832 DEBUG 1005 odoo.modules.loading: Loading module digest_enterprise (54/160) 
2025-05-25 18:59:42,196 30832 DEBUG 1005 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,196 30832 DEBUG 1005 odoo.modules.loading: Loading module hr (55/160) 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Module hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard (56/160) 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_edition (57/160) 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,198 30832 DEBUG 1005 odoo.modules.loading: Loading module stock (58/160) 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Module stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant (59/160) 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Loading module account_batch_payment (60/160) 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Module account_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,200 30832 DEBUG 1005 odoo.modules.loading: Loading module account_check_printing (61/160) 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Module account_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Loading module account_edi_ubl_cii (62/160) 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Module account_edi_ubl_cii loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Loading module account_external_tax (63/160) 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Module account_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Loading module account_payment (64/160) 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Module account_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,201 30832 DEBUG 1005 odoo.modules.loading: Loading module analytic_enterprise (65/160) 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Module analytic_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Loading module currency_rate_live (66/160) 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Module currency_rate_live loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense (67/160) 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_gantt (68/160) 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Module hr_gantt loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_mobile (69/160) 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Module hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,202 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_org_chart (70/160) 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Module hr_org_chart loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_skills (71/160) 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Module hr_skills loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_account (72/160) 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Loading module mail_bot_hr (73/160) 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Module mail_bot_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,203 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase (74/160) 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Module purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account (75/160) 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Module snailmail_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_account (76/160) 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account (77/160) 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_edition (78/160) 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_edition loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,204 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_account (79/160) 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Module stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_barcode (80/160) 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Module stock_barcode loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_enterprise (81/160) 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Module stock_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_sms (82/160) 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Module stock_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,205 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_batch_payment (83/160) 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant_batch_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Loading module account_accountant_check_printing (84/160) 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Module account_accountant_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Loading module account_auto_transfer (85/160) 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Module account_auto_transfer loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax (86/160) 
2025-05-25 18:59:42,206 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import (87/160) 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Loading module account_base_import (88/160) 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Module account_base_import loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Loading module account_online_synchronization (89/160) 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Module account_online_synchronization loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Loading module accountant (90/160) 
2025-05-25 18:59:42,207 30832 DEBUG 1005 odoo.modules.loading: Module accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_predict_product (91/160) 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense_predict_product loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_1099 (92/160) 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_1099 loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_check_printing (93/160) 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_check_printing loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_payment_nacha (94/160) 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_payment_nacha loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,208 30832 DEBUG 1005 odoo.modules.loading: Loading module point_of_sale (95/160) 
2025-05-25 18:59:42,209 30832 DEBUG 1005 odoo.modules.loading: Module point_of_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,209 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase_edi_ubl_bis3 (96/160) 
2025-05-25 18:59:42,210 30832 DEBUG 1005 odoo.modules.loading: Module purchase_edi_ubl_bis3 loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,210 30832 DEBUG 1005 odoo.modules.loading: Loading module purchase_stock (97/160) 
2025-05-25 18:59:42,210 30832 DEBUG 1005 odoo.modules.loading: Module purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,210 30832 DEBUG 1005 odoo.modules.loading: Loading module sale (98/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock (99/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_stock_account (100/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_stock_account loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module stock_accountant (101/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module stock_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_camt (102/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_camt loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_csv (103/160) 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_csv loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,211 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_import_ofx (104/160) 
2025-05-25 18:59:42,212 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_import_ofx loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,212 30832 DEBUG 1005 odoo.modules.loading: Loading module account_extract (105/160) 
2025-05-25 18:59:42,212 30832 DEBUG 1005 odoo.modules.loading: Module account_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,212 30832 DEBUG 1005 odoo.modules.loading: Loading module account_reports (106/160) 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Module account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Loading module hr_expense_extract (107/160) 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Module hr_expense_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_avatax (108/160) 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Module pos_avatax loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_barcodelookup (109/160) 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Module pos_barcodelookup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,213 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_enterprise (110/160) 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Module pos_enterprise loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_epson_printer (111/160) 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Module pos_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr (112/160) 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment (113/160) 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_preparation_display (114/160) 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Module pos_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,214 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant (115/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_sms (116/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module pos_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_account_accountant (117/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module sale_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_async_emails (118/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module sale_async_emails loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_edi_ubl (119/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module sale_edi_ubl loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_external_tax (120/160) 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Module sale_external_tax loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,215 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_management (121/160) 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Module sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase (122/160) 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Module sale_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_sms (123/160) 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Module sale_sms loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_stock (124/160) 
2025-05-25 18:59:42,216 30832 DEBUG 1005 odoo.modules.loading: Module sale_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_purchase_stock (125/160) 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_sale (126/160) 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module account_asset (127/160) 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Module account_asset loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_sale (128/160) 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module account_bank_statement_extract (129/160) 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Module account_bank_statement_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,217 30832 DEBUG 1005 odoo.modules.loading: Loading module account_disallowed_expenses (130/160) 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Module account_disallowed_expenses loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Loading module account_followup (131/160) 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Module account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract (132/160) 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Loading module account_reports_cash_basis (133/160) 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Module account_reports_cash_basis loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Loading module l10n_us_reports (134/160) 
2025-05-25 18:59:42,218 30832 DEBUG 1005 odoo.modules.loading: Module l10n_us_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_account_reports (135/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_account_reports loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_mobile (136/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_mobile loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_preparation_display (137/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_hr_restaurant (138/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_hr_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_restaurant_preparation_display (139/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_restaurant_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_sale (140/160) 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Module pos_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,219 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order (141/160) 
2025-05-25 18:59:42,220 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,220 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_expense (142/160) 
2025-05-25 18:59:42,220 30832 DEBUG 1005 odoo.modules.loading: Module sale_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,220 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_pdf_quote_builder (143/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module sale_pdf_quote_builder loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Loading module sale_purchase_stock (144/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module sale_purchase_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_account_accountant (145/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_account_accountant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_hr (146/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_hr loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_pos_restaurant (147/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_pos_restaurant loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_sale_management (148/160) 
2025-05-25 18:59:42,221 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_sale_management loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Loading module account_avatax_stock (149/160) 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Module account_avatax_stock loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Loading module account_invoice_extract_purchase (150/160) 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Module account_invoice_extract_purchase loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Loading module account_loans (151/160) 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Module account_loans loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order (152/160) 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,222 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_order_tracking_display (153/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_order_tracking_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_epson_printer (154/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_epson_printer loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_preparation_display (155/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_self_order_sale (156/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_self_order_sale loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_settle_due (157/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_settle_due loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module snailmail_account_followup (158/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module snailmail_account_followup loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module spreadsheet_dashboard_hr_expense (159/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module spreadsheet_dashboard_hr_expense loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Loading module pos_online_payment_self_order_preparation_display (160/160) 
2025-05-25 18:59:42,223 30832 DEBUG 1005 odoo.modules.loading: Module pos_online_payment_self_order_preparation_display loaded in 0.00s, 0 queries 
2025-05-25 18:59:42,223 30832 INFO 1005 odoo.modules.loading: 160 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-05-25 18:59:42,445 30832 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:59:42,459 30832 DEBUG 1005 odoo.modules.registry: Invalidating templates model caches from write /odoo/180/odoo/addons/base/models/ir_ui_view.py:533 
2025-05-25 18:59:42,871 30832 INFO 1005 odoo.modules.loading: Modules loaded. 
2025-05-25 18:59:42,879 30832 INFO 1005 odoo.modules.registry: Registry changed, signaling through the database 
2025-05-25 18:59:42,879 30832 INFO 1005 odoo.modules.registry: Registry loaded in 1.346s 
2025-05-25 18:59:42,881 30832 INFO 1005 odoo.modules.registry: Registry changed, signaling through the database 
2025-05-25 18:59:42,882 30832 INFO 1005 odoo.modules.registry: Registry loaded in 2.468s 
2025-05-25 18:59:42,882 30832 INFO 1005 odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-05-25 18:59:42,884 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:42] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 7083 1.282 1.199
2025-05-25 18:59:42,898 30832 INFO 1005 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-05-25 18:59:43,209 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "GET /odoo HTTP/1.1" 200 - 108 0.026 0.286
2025-05-25 18:59:43,232 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "GET /web/webclient/translations/89741f3f5dc6ce4a748b4d6e684858bf6ed2f590?lang=en_US HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:59:43,628 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "POST /mail/data HTTP/1.1" 200 - 36 0.010 0.012
2025-05-25 18:59:43,663 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.007
2025-05-25 18:59:43,671 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 4 0.001 0.003
2025-05-25 18:59:43,881 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:43] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.003 0.004
2025-05-25 18:59:44,000 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:44] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.003
2025-05-25 18:59:44,904 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:44] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:59:45,668 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:45] "POST /web/action/load HTTP/1.1" 200 - 11 0.002 0.005
2025-05-25 18:59:45,914 30832 DEBUG 1005 odoo.api: call ir.module.module().get_views(options={'action_id': 39, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'kanban'], [False, 'list'], [False, 'form'], [95, 'search']]) 
2025-05-25 18:59:45,941 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:45] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.010 0.021
2025-05-25 18:59:45,990 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('module_type', category_domain=[], enable_counters=False, expand=True, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:59:45,991 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 18:59:46,257 30832 DEBUG 1005 odoo.api: call ir.module.module().web_search_read(count_limit=10001, domain=[['application', '=', True]], limit=80, offset=0, order='', specification={'to_buy': {}, 'name': {}, 'state': {}, 'summary': {}, 'website': {}, 'application': {}, 'module_type': {}, 'icon': {}, 'icon_flag': {}, 'shortdesc': {}}) 
2025-05-25 18:59:46,258 30832 DEBUG 1005 odoo.api: call ir.module.module().search_panel_select_range('category_id', category_domain=[], enable_counters=True, expand=False, filter_domain=[], hierarchize=True, limit=200, search_domain=[['application', '=', True]]) 
2025-05-25 18:59:46,277 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:46] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.002 0.020
2025-05-25 18:59:46,283 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:46] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 53 0.017 0.012
2025-05-25 18:59:46,369 30832 DEBUG 1005 odoo.addons.http_routing.models.ir_http: '/wbl_pos_multi_currency/static/description/icon.png' (lang: 'en') no lang in url and default website, continue 
2025-05-25 18:59:46,449 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 18:59:46] "GET /wbl_pos_multi_currency/static/description/icon.png HTTP/1.1" 404 - 63 0.014 0.068
2025-05-25 19:00:22,734 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:00:23,699 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:01:22,793 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:01:24,781 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:02:22,855 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:02:25,860 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:03:02,463 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:02] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.007
2025-05-25 19:03:02,723 30832 DEBUG 1005 odoo.api: call pos.config().get_views(options={'action_id': 544, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'kanban'], [False, 'list'], [False, 'form'], [1489, 'search']]) 
2025-05-25 19:03:02,768 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:02] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 63 0.016 0.031
2025-05-25 19:03:02,799 30832 DEBUG 1005 odoo.api: call pos.config().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'cash_control': {}, 'current_session_id': {'fields': {'display_name': {}}}, 'current_session_state': {}, 'access_token': {}, 'pos_session_state': {}, 'pos_session_duration': {}, 'currency_id': {'fields': {'display_name': {}}}, 'self_ordering_mode': {}, 'name': {}, 'pos_session_username': {}, 'last_session_closing_date': {}, 'last_session_closing_cash': {}, 'number_of_rescue_session': {}, 'current_user_id': {'fields': {'display_name': {}}}, 'customer_display_type': {}}) 
2025-05-25 19:03:02,810 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:02] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 14 0.005 0.008
2025-05-25 19:03:03,085 30832 DEBUG 1005 odoo.api: call pos.config().get_pos_kanban_view_state() 
2025-05-25 19:03:03,091 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:03] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 10 0.002 0.005
2025-05-25 19:03:04,499 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:04] "POST /web/action/load HTTP/1.1" 200 - 9 0.002 0.005
2025-05-25 19:03:04,837 30832 DEBUG 1005 odoo.api: call pos.order().get_views(options={'action_id': 527, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'list'], [False, 'form'], [False, 'kanban'], [False, 'pivot'], [False, 'search']]) 
2025-05-25 19:03:04,881 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:04] "POST /web/dataset/call_kw/pos.order/get_views HTTP/1.1" 200 - 54 0.013 0.034
2025-05-25 19:03:05,085 30832 DEBUG 1005 odoo.api: call pos.order().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'currency_id': {'fields': {}}, 'name': {}, 'session_id': {'fields': {'display_name': {}}}, 'date_order': {}, 'config_id': {'fields': {'display_name': {}}}, 'pos_reference': {}, 'tracking_number': {}, 'partner_id': {'fields': {'display_name': {}}}, 'employee_id': {'fields': {'display_name': {}}}, 'amount_total': {}, 'state': {}, 'is_edited': {}}) 
2025-05-25 19:03:05,098 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:05] "POST /web/dataset/call_kw/pos.order/web_search_read HTTP/1.1" 200 - 16 0.005 0.010
2025-05-25 19:03:05,210 30832 DEBUG 1005 odoo.api: call res.users(2,).has_group('base.group_allow_export') 
2025-05-25 19:03:05,210 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:05] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.002
2025-05-25 19:03:05,239 30832 DEBUG 1005 odoo.api: call res.users(2,).has_group('hr.group_hr_user') 
2025-05-25 19:03:05,239 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:05] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.003
2025-05-25 19:03:11,654 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:11] "GET /odoo/pos-orders?debug=1 HTTP/1.1" 200 - 20 0.004 0.012
2025-05-25 19:03:11,896 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:11] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 3 0.001 0.003
2025-05-25 19:03:11,945 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:11] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.008
2025-05-25 19:03:12,253 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /web/action/load HTTP/1.1" 200 - 10 0.002 0.005
2025-05-25 19:03:12,260 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.005
2025-05-25 19:03:12,270 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /mail/data HTTP/1.1" 200 - 22 0.006 0.011
2025-05-25 19:03:12,277 30832 DEBUG 1005 odoo.api: call pos.order().get_views(options={'action_id': 527, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'list'], [False, 'form'], [False, 'kanban'], [False, 'pivot'], [False, 'search']]) 
2025-05-25 19:03:12,290 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /web/dataset/call_kw/pos.order/get_views HTTP/1.1" 200 - 3 0.002 0.014
2025-05-25 19:03:12,575 30832 DEBUG 1005 odoo.api: call pos.order().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'currency_id': {'fields': {}}, 'name': {}, 'session_id': {'fields': {'display_name': {}}}, 'date_order': {}, 'config_id': {'fields': {'display_name': {}}}, 'pos_reference': {}, 'tracking_number': {}, 'partner_id': {'fields': {'display_name': {}}}, 'employee_id': {'fields': {'display_name': {}}}, 'amount_total': {}, 'state': {}, 'is_edited': {}}) 
2025-05-25 19:03:12,586 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /web/dataset/call_kw/pos.order/web_search_read HTTP/1.1" 200 - 9 0.004 0.010
2025-05-25 19:03:12,607 30832 DEBUG 1005 odoo.api: call res.users(2,).has_group('base.group_allow_export') 
2025-05-25 19:03:12,610 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.006
2025-05-25 19:03:12,612 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.005
2025-05-25 19:03:12,967 30832 DEBUG 1005 odoo.api: call res.users(2,).has_group('hr.group_hr_user') 
2025-05-25 19:03:12,967 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:12] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.002
2025-05-25 19:03:14,317 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:14] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.001
2025-05-25 19:03:16,147 30832 DEBUG 1005 odoo.api: call ir.ui.view().has_access('write') 
2025-05-25 19:03:16,148 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:16] "POST /web/dataset/call_kw/ir.ui.view/has_access HTTP/1.1" 200 - 1 0.001 0.003
2025-05-25 19:03:16,149 30832 DEBUG 1005 odoo.api: call ir.model.access().has_access('read') 
2025-05-25 19:03:16,150 30832 DEBUG 1005 odoo.api: call ir.rule().has_access('read') 
2025-05-25 19:03:16,150 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:16] "POST /web/dataset/call_kw/ir.model.access/has_access HTTP/1.1" 200 - 1 0.001 0.006
2025-05-25 19:03:16,151 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:16] "POST /web/dataset/call_kw/ir.rule/has_access HTTP/1.1" 200 - 1 0.000 0.005
2025-05-25 19:03:22,910 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:03:26,929 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:03:39,546 30832 DEBUG 1005 odoo.api: call ir.ui.view().get_views(options={'action_id': False, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': False}, views=[[False, 'list'], [False, 'search']]) 
2025-05-25 19:03:39,558 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:39] "POST /web/dataset/call_kw/ir.ui.view/get_views HTTP/1.1" 200 - 17 0.005 0.010
2025-05-25 19:03:39,885 30832 DEBUG 1005 odoo.api: call ir.ui.view().web_search_read(count_limit=10001, domain=['&', ['type', '!=', 'qweb'], ['type', '!=', 'search']], limit=80, offset=0, order='priority ASC, id ASC', specification={'priority': {}, 'name': {}, 'type': {}, 'model': {}, 'xml_id': {}, 'inherit_id': {'fields': {'display_name': {}}}}) 
2025-05-25 19:03:39,891 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:03:39] "POST /web/dataset/call_kw/ir.ui.view/web_search_read HTTP/1.1" 200 - 6 0.004 0.005
2025-05-25 19:03:40,131 30832 INFO ? werkzeug: 127.0.0.1 - - [25/May/2025 19:03:40] "GET /web/static/img/openhand.cur HTTP/1.1" 200 - 0 0.000 0.001
2025-05-25 19:04:10,414 30832 DEBUG 1005 odoo.api: call ir.ui.view().get_views(options={'action_id': False, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'form'], [False, 'search']]) 
2025-05-25 19:04:10,426 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:10] "POST /web/dataset/call_kw/ir.ui.view/get_views HTTP/1.1" 200 - 14 0.004 0.011
2025-05-25 19:04:10,740 30832 DEBUG 1005 odoo.api: call ir.ui.view(1461,).web_read(specification={'name': {}, 'type': {}, 'model_id': {'fields': {'display_name': {}}}, 'priority': {}, 'active': {}, 'inherit_id': {'fields': {'display_name': {}}}, 'mode': {}, 'model_data_id': {'fields': {'display_name': {}}}, 'xml_id': {}, 'warning_info': {}, 'arch_db': {}, 'arch_base': {}, 'groups_id': {'fields': {'full_name': {}}, 'limit': 40, 'order': ''}, 'inherit_children_ids': {'fields': {'id': {}, 'priority': {}, 'name': {}, 'xml_id': {}, 'active': {}}, 'context': {'default_mode': 'extension', 'active_test': False}, 'limit': 40, 'order': 'priority ASC, id ASC'}, 'model': {}, 'display_name': {}}) 
2025-05-25 19:04:10,748 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:10] "POST /web/dataset/call_kw/ir.ui.view/web_read HTTP/1.1" 200 - 13 0.003 0.007
2025-05-25 19:04:11,008 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:11] "GET /web/bundle/web.ace_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 2 0.000 0.002
2025-05-25 19:04:11,096 30832 INFO 1005 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/5d9d3f8/web.ace_lib.min.js (id:635) 
2025-05-25 19:04:11,099 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:11] "GET /web/assets/5d9d3f8/web.ace_lib.min.js HTTP/1.1" 200 - 11 0.003 0.013
2025-05-25 19:04:22,965 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:04:27,967 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:04:29,192 30832 DEBUG 1005 odoo.api: call pos.order().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'currency_id': {'fields': {}}, 'name': {}, 'session_id': {'fields': {'display_name': {}}}, 'date_order': {}, 'config_id': {'fields': {'display_name': {}}}, 'pos_reference': {}, 'tracking_number': {}, 'partner_id': {'fields': {'display_name': {}}}, 'employee_id': {'fields': {'display_name': {}}}, 'amount_total': {}, 'state': {}, 'is_edited': {}}) 
2025-05-25 19:04:29,204 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:29] "POST /web/dataset/call_kw/pos.order/web_search_read HTTP/1.1" 200 - 9 0.004 0.010
2025-05-25 19:04:35,979 30832 DEBUG 1005 odoo.api: call pos.order(34,).web_read(specification={'state': {}, 'has_refundable_lines': {}, 'failed_pickings': {}, 'picking_count': {}, 'sale_order_count': {}, 'refund_orders_count': {}, 'refunded_order_id': {'fields': {'display_name': {}}}, 'name': {}, 'date_order': {}, 'session_id': {'fields': {'display_name': {}}}, 'employee_id': {'fields': {'display_name': {}}}, 'user_id': {'fields': {'display_name': {}}}, 'order_edit_tracking': {}, 'is_edited': {}, 'partner_id': {'fields': {'display_name': {}}, 'context': {'res_partner_search_mode': 'customer'}}, 'fiscal_position_id': {'fields': {'display_name': {}}}, 'table_id': {'fields': {'display_name': {}}}, 'customer_count': {}, 'lines': {'fields': {'name': {}, 'full_product_name': {}, 'product_id': {'fields': {'display_name': {}}}, 'is_edited': {}, 'qty': {}, 'customer_note': {}, 'price_unit': {}, 'is_total_cost_computed': {}, 'total_cost': {}, 'margin': {}, 'margin_percent': {}, 'discount': {}, 'tax_ids_after_fiscal_position': {'fields': {'display_name': {}}}, 'tax_ids': {}, 'price_subtotal': {}, 'price_subtotal_incl': {}, 'currency_id': {'fields': {}}, 'refunded_qty': {}, 'notice': {}}, 'limit': 40, 'order': ''}, 'amount_tax': {}, 'amount_total': {}, 'amount_paid': {}, 'margin': {}, 'margin_percent': {}, 'is_total_cost_computed': {}, 'currency_id': {'fields': {}}, 'payment_ids': {'fields': {'currency_id': {'fields': {}}, 'payment_date': {}, 'payment_method_id': {'fields': {'display_name': {}}}, 'amount': {}, 'payment_method_payment_mode': {}, 'card_no': {}, 'card_brand': {}, 'cardholder_name': {}}, 'limit': 40, 'order': ''}, 'amount_difference': {}, 'session_move_id': {'fields': {'display_name': {}}}, 'pos_reference': {}, 'tracking_number': {}, 'country_code': {}, 'pricelist_id': {'fields': {'display_name': {}}}, 'floating_order_name': {}, 'email': {}, 'mobile': {}, 'general_note': {}, 'available_payment_method_ids': {}, 'nb_print': {}, 'display_name': {}}) 
2025-05-25 19:04:36,012 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:36] "POST /web/dataset/call_kw/pos.order/web_read HTTP/1.1" 200 - 43 0.014 0.022
2025-05-25 19:04:36,262 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:36] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.008 0.011
2025-05-25 19:04:36,415 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:36] "POST /mail/thread/messages HTTP/1.1" 200 - 19 0.006 0.009
2025-05-25 19:04:36,458 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:36] "GET /web/image/res.partner/3/avatar_128?unique=1748007728000 HTTP/1.1" 200 - 8 0.003 0.007
2025-05-25 19:04:45,280 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:45] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.005
2025-05-25 19:04:45,541 30832 DEBUG 1005 odoo.api: call pos.session().get_views(options={'action_id': 547, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'list'], [False, 'kanban'], [False, 'form'], [1495, 'search']]) 
2025-05-25 19:04:45,564 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:45] "POST /web/dataset/call_kw/pos.session/get_views HTTP/1.1" 200 - 31 0.007 0.018
2025-05-25 19:04:45,600 30832 DEBUG 1005 odoo.api: call pos.session().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'name': {}, 'config_id': {'fields': {'display_name': {}}}, 'user_id': {'fields': {'display_name': {}}}, 'start_at': {}, 'stop_at': {}, 'cash_register_balance_start': {}, 'cash_register_balance_end_real': {}, 'cash_register_balance_end': {}, 'state': {}}) 
2025-05-25 19:04:45,633 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:45] "POST /web/dataset/call_kw/pos.session/web_search_read HTTP/1.1" 200 - 46 0.012 0.023
2025-05-25 19:04:45,887 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:45] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 9 0.003 0.007
2025-05-25 19:04:50,027 30832 DEBUG 1005 odoo.api: call ir.ui.view(1493,).web_read(specification={'name': {}, 'type': {}, 'model_id': {'fields': {'display_name': {}}}, 'priority': {}, 'active': {}, 'inherit_id': {'fields': {'display_name': {}}}, 'mode': {}, 'model_data_id': {'fields': {'display_name': {}}}, 'xml_id': {}, 'warning_info': {}, 'arch_db': {}, 'arch_base': {}, 'groups_id': {'fields': {'full_name': {}}, 'limit': 40, 'order': ''}, 'inherit_children_ids': {'fields': {'id': {}, 'priority': {}, 'name': {}, 'xml_id': {}, 'active': {}}, 'context': {'default_mode': 'extension', 'active_test': False}, 'limit': 40, 'order': 'priority ASC, id ASC'}, 'model': {}, 'display_name': {}}) 
2025-05-25 19:04:50,034 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:04:50] "POST /web/dataset/call_kw/ir.ui.view/web_read HTTP/1.1" 200 - 9 0.002 0.007
2025-05-25 19:05:03,842 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:05:03] "POST /web/action/load HTTP/1.1" 200 - 9 0.002 0.005
2025-05-25 19:05:04,098 30832 DEBUG 1005 odoo.api: call pos.payment().get_views(options={'action_id': 543, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'list'], [False, 'form'], [False, 'search']]) 
2025-05-25 19:05:04,108 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:05:04] "POST /web/dataset/call_kw/pos.payment/get_views HTTP/1.1" 200 - 14 0.003 0.009
2025-05-25 19:05:04,162 30832 DEBUG 1005 odoo.api: call pos.payment().web_read_group(domain=[], fields=['amount:sum'], groupby=['payment_method_id'], lazy=True, limit=80, offset=0, orderby='') 
2025-05-25 19:05:04,164 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:05:04] "POST /web/dataset/call_kw/pos.payment/web_read_group HTTP/1.1" 200 - 3 0.001 0.003
2025-05-25 19:05:07,990 30832 DEBUG 1005 odoo.api: call ir.ui.view(1493,).web_read(specification={'name': {}, 'type': {}, 'model_id': {'fields': {'display_name': {}}}, 'priority': {}, 'active': {}, 'inherit_id': {'fields': {'display_name': {}}}, 'mode': {}, 'model_data_id': {'fields': {'display_name': {}}}, 'xml_id': {}, 'warning_info': {}, 'arch_db': {}, 'arch_base': {}, 'groups_id': {'fields': {'full_name': {}}, 'limit': 40, 'order': ''}, 'inherit_children_ids': {'fields': {'id': {}, 'priority': {}, 'name': {}, 'xml_id': {}, 'active': {}}, 'context': {'default_mode': 'extension', 'active_test': False}, 'limit': 40, 'order': 'priority ASC, id ASC'}, 'model': {}, 'display_name': {}}) 
2025-05-25 19:05:07,996 30832 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 19:05:07] "POST /web/dataset/call_kw/ir.ui.view/web_read HTTP/1.1" 200 - 9 0.003 0.006
2025-05-25 19:05:23,027 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:05:29,083 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-25 19:06:23,093 30832 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-25 19:06:30,151 30832 DEBUG ? odoo.service.server: cron1 polling for jobs 
