2025-05-25 17:00:01,870 1384 DEBUG 1005 odoo.api: call pos.order().sync_from_ui([{'message_follower_ids': [], 'message_ids': [], 'website_message_ids': [], 'access_token': '9274e9f2-6ef6-46d2-a1ca-e57a7a246212', 'name': 'Order 00047-001-0001', 'last_order_preparation_change': '{"lines":{},"generalNote":"","sittingMode":"dine in"}', 'date_order': '2025-05-25 17:00:01', 'user_id': 2, 'amount_difference': False, 'amount_tax': 150, 'amount_total': 1150, 'amount_paid': 1150, 'amount_return': 0, 'lines': [[0, 0, {'skip_change': False, 'product_id': 20, 'attribute_value_ids': [], 'custom_attribute_value_ids': [], 'price_unit': 25, 'qty': 20, 'price_subtotal': 500, 'price_subtotal_incl': 575, 'price_extra': 0, 'price_type': 'original', 'discount': False, 'order_id': 26, 'tax_ids': [[4, 1]], 'pack_lot_ids': [], 'full_product_name': 'Classic Leather Belt', 'customer_note': False, 'refund_orderline_ids': [], 'refunded_orderline_id': False, 'uuid': '878765e4-4927-4e13-a075-9f254bbfa100', 'note': '', 'combo_parent_id': False, 'combo_line_ids': [], 'combo_item_id': False, 'write_date': False, 'sale_order_origin_id': False, 'sale_order_line_id': False, 'down_payment_details': False, 'id': 1}], [0, 0, {'skip_change': False, 'product_id': 15, 'attribute_value_ids': [], 'custom_attribute_value_ids': [], 'price_unit': 50, 'qty': 10, 'price_subtotal': 500, 'price_subtotal_incl': 575, 'price_extra': 0, 'price_type': 'original', 'discount': False, 'order_id': 26, 'tax_ids': [[4, 1]], 'pack_lot_ids': [], 'full_product_name': 'Classic Brown Jacket', 'customer_note': False, 'refund_orderline_ids': [], 'refunded_orderline_id': False, 'uuid': '2bcdb93e-2d7d-4347-b5ae-71eb5c492294', 'note': '', 'combo_parent_id': False, 'combo_line_ids': [], 'combo_item_id': False, 'write_date': False, 'sale_order_origin_id': False, 'sale_order_line_id': False, 'down_payment_details': False, 'id': 2}]], 'company_id': 1, 'pricelist_id': False, 'partner_id': False, 'sequence_number': 1, 'session_id': 53, 'state': 'paid', 'account_move': False, 'picking_ids': [], 'procurement_group_id': False, 'floating_order_name': False, 'general_note': '', 'nb_print': 0, 'pos_reference': '00047-001-0001', 'fiscal_position_id': False, 'payment_ids': [[0, 0, {'name': False, 'pos_order_id': 26, 'amount': 1150, 'payment_method_id': 5, 'payment_date': '2025-05-25 16:59:21', 'card_type': False, 'card_brand': False, 'card_no': False, 'cardholder_name': False, 'payment_ref_no': False, 'payment_method_authcode': False, 'payment_method_issuer_bank': False, 'payment_method_payment_mode': False, 'transaction_id': False, 'payment_status': False, 'ticket': '', 'is_change': False, 'account_move_id': False, 'uuid': '5a06fe0c-ace3-4d49-8334-36497bdda89a', 'id': 2, 'create_uid': False, 'create_date': False, 'write_uid': False, 'write_date': False, 'payment_currency_id': False, 'payment_currency_rate': False, 'amount_in_payment_currency': False, 'online_account_payment_id': False}]], 'to_invoice': False, 'shipping_date': False, 'is_tipped': False, 'tip_amount': False, 'ticket_code': 'cpkyc', 'uuid': '0e1ad10c-a039-4af6-9d3f-bfdd6758a023', 'has_deleted_line': False, 'id': '26', 'create_uid': False, 'create_date': False, 'write_uid': False, 'write_date': False, 'employee_id': False, 'payment_currency_id': False, 'payment_currency_rate': False, 'amount_in_payment_currency': False, 'amount_paid_in_payment_currency': False, 'next_online_payment_amount': False, 'table_id': False, 'customer_count': False, 'takeaway': False, 'crm_team_id': False, 'table_stand_number': False}]) 
2025-05-25 17:00:01,870 1384 INFO 1005 odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #******** started for PoS orders references: [{'name': 'Order 00047-001-0001', 'uuid': '0e1ad10c-a039-4af6-9d3f-bfdd6758a023'}] 
2025-05-25 17:00:01,871 1384 DEBUG 1005 odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #******** processing order {'name': 'Order 00047-001-0001', 'uuid': '0e1ad10c-a039-4af6-9d3f-bfdd6758a023'} order full data: {'access_token': '9274e9f2-6ef6-46d2-a1ca-e57a7a246212',
 'account_move': False,
 'amount_difference': False,
 'amount_in_payment_currency': False,
 'amount_paid': 1150,
 'amount_paid_in_payment_currency': False,
 'amount_return': 0,
 'amount_tax': 150,
 'amount_total': 1150,
 'company_id': 1,
 'create_date': False,
 'create_uid': False,
 'crm_team_id': False,
 'customer_count': False,
 'date_order': '2025-05-25 17:00:01',
 'employee_id': False,
 'fiscal_position_id': False,
 'floating_order_name': False,
 'general_note': '',
 'has_deleted_line': False,
 'id': '26',
 'is_tipped': False,
 'last_order_preparation_change': '{"lines":{},"generalNote":"","sittingMode":"dine '
                                  'in"}',
 'lines': [[0,
            0,
            {'attribute_value_ids': [],
             'combo_item_id': False,
             'combo_line_ids': [],
             'combo_parent_id': False,
             'custom_attribute_value_ids': [],
             'customer_note': False,
             'discount': False,
             'down_payment_details': False,
             'full_product_name': 'Classic Leather Belt',
             'id': 1,
             'note': '',
             'order_id': 26,
             'pack_lot_ids': [],
             'price_extra': 0,
             'price_subtotal': 500,
             'price_subtotal_incl': 575,
             'price_type': 'original',
             'price_unit': 25,
             'product_id': 20,
             'qty': 20,
             'refund_orderline_ids': [],
             'refunded_orderline_id': False,
             'sale_order_line_id': False,
             'sale_order_origin_id': False,
             'skip_change': False,
             'tax_ids': [[4, 1]],
             'uuid': '878765e4-4927-4e13-a075-9f254bbfa100',
             'write_date': False}],
           [0,
            0,
            {'attribute_value_ids': [],
             'combo_item_id': False,
             'combo_line_ids': [],
             'combo_parent_id': False,
             'custom_attribute_value_ids': [],
             'customer_note': False,
             'discount': False,
             'down_payment_details': False,
             'full_product_name': 'Classic Brown Jacket',
             'id': 2,
             'note': '',
             'order_id': 26,
             'pack_lot_ids': [],
             'price_extra': 0,
             'price_subtotal': 500,
             'price_subtotal_incl': 575,
             'price_type': 'original',
             'price_unit': 50,
             'product_id': 15,
             'qty': 10,
             'refund_orderline_ids': [],
             'refunded_orderline_id': False,
             'sale_order_line_id': False,
             'sale_order_origin_id': False,
             'skip_change': False,
             'tax_ids': [[4, 1]],
             'uuid': '2bcdb93e-2d7d-4347-b5ae-71eb5c492294',
             'write_date': False}]],
 'message_follower_ids': [],
 'message_ids': [],
 'name': 'Order 00047-001-0001',
 'nb_print': 0,
 'next_online_payment_amount': False,
 'partner_id': False,
 'payment_currency_id': False,
 'payment_currency_rate': False,
 'payment_ids': [[0,
                  0,
                  {'account_move_id': False,
                   'amount': 1150,
                   'amount_in_payment_currency': False,
                   'card_brand': False,
                   'card_no': False,
                   'card_type': False,
                   'cardholder_name': False,
                   'create_date': False,
                   'create_uid': False,
                   'id': 2,
                   'is_change': False,
                   'name': False,
                   'online_account_payment_id': False,
                   'payment_currency_id': False,
                   'payment_currency_rate': False,
                   'payment_date': '2025-05-25 16:59:21',
                   'payment_method_authcode': False,
                   'payment_method_id': 5,
                   'payment_method_issuer_bank': False,
                   'payment_method_payment_mode': False,
                   'payment_ref_no': False,
                   'payment_status': False,
                   'pos_order_id': 26,
                   'ticket': '',
                   'transaction_id': False,
                   'uuid': '5a06fe0c-ace3-4d49-8334-36497bdda89a',
                   'write_date': False,
                   'write_uid': False}]],
 'picking_ids': [],
 'pos_reference': '00047-001-0001',
 'pricelist_id': False,
 'procurement_group_id': False,
 'sequence_number': 1,
 'session_id': 53,
 'shipping_date': False,
 'state': 'paid',
 'table_id': False,
 'table_stand_number': False,
 'takeaway': False,
 'ticket_code': 'cpkyc',
 'tip_amount': False,
 'to_invoice': False,
 'user_id': 2,
 'uuid': '0e1ad10c-a039-4af6-9d3f-bfdd6758a023',
 'website_message_ids': [],
 'write_date': False,
 'write_uid': False} 
2025-05-25 17:00:01,874 1384 ERROR 1005 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2386, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1913, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 1976, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/service/model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "/odoo/180/odoo/http.py", line 1943, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2191, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/addons/base/models/ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 740, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/web/controllers/dataset.py", line 36, in call_kw
    return call_kw(request.env[model], method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_self_order/models/pos_order.py", line 53, in sync_from_ui
    result = super().sync_from_ui(orders)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_sale/models/pos_order.py", line 51, in sync_from_ui
    data = super().sync_from_ui(orders)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/pos_restaurant/models/pos_order.py", line 33, in sync_from_ui
    result = super().sync_from_ui(orders)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/custom/Ent180/pos_preparation_display/models/pos_order.py", line 11, in sync_from_ui
    data = super().sync_from_ui(orders)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/addons/point_of_sale/models/pos_order.py", line 1052, in sync_from_ui
    order_ids.append(self._process_order(order, False))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: PosOrder._process_order() missing 1 required positional argument: 'existing_order'
2025-05-25 17:00:01,875 1384 INFO 1005 werkzeug: 127.0.0.1 - - [25/May/2025 17:00:01] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 4 0.001 0.005
