2025-05-26 04:33:30,543 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:33:30,545 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 1 acquired 
2025-05-26 04:33:30,574 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: cron.object.execute('1006', 1, '*', 'Base: Auto-vacuum internal data', 35) 
2025-05-26 04:33:30,574 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-05-26 04:33:30,579 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling wizard.ir.model.menu.create()._transient_vacuum() 
2025-05-26 04:33:30,582 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling reset.view.arch.wizard()._transient_vacuum() 
2025-05-26 04:33:30,587 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.attachment()._gc_file_store() 
2025-05-26 04:33:30,590 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/c5/c53f357dbd21fe7627f749caf14cb9a2082c72fc 
2025-05-26 04:33:30,590 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/f0/f06b23a8f6acaa6fbf7b5a5509c435ff40c6a179 
2025-05-26 04:33:30,591 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/3e/3e09f1a79642328618202326d6caf027a55c5a63 
2025-05-26 04:33:30,592 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/6f/6f0a21e1f0879e6ef8c00f119384caaef563c2d2 
2025-05-26 04:33:30,592 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/77/77291c95d3ea70c8a555e022450079f0303783ff 
2025-05-26 04:33:30,592 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/4b/4ba19a677bda75931298336759d6f2124182d18f 
2025-05-26 04:33:30,593 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/a5/a5252e79191450674271d3e065245e48d58ecfa2 
2025-05-26 04:33:30,593 9899 DEBUG 1006 odoo.addons.base.models.ir_attachment: _file_gc unlinked /root/.local/share/Odoo/filestore/1006/c7/c70bd1c6b93872b5a7c4e823d2a4d028796810d2 
2025-05-26 04:33:30,593 9899 INFO 1006 odoo.addons.base.models.ir_attachment: filestore gc 18 checked, 8 removed 
2025-05-26 04:33:30,595 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.cron.trigger()._gc_cron_triggers() 
2025-05-26 04:33:30,596 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.cron.progress()._gc_cron_progress() 
2025-05-26 04:33:30,607 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.http()._gc_sessions() 
2025-05-26 04:33:30,614 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.demo()._transient_vacuum() 
2025-05-26 04:33:30,615 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.demo_failure()._transient_vacuum() 
2025-05-26 04:33:30,615 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.demo_failure.wizard()._transient_vacuum() 
2025-05-26 04:33:30,616 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling ir.profile()._gc_profile() 
2025-05-26 04:33:30,618 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.enable.profiling.wizard()._transient_vacuum() 
2025-05-26 04:33:30,626 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.config()._transient_vacuum() 
2025-05-26 04:33:30,628 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.config.settings()._transient_vacuum() 
2025-05-26 04:33:30,632 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.users.log()._gc_user_logs() 
2025-05-26 04:33:30,632 9899 INFO 1006 odoo.addons.base.models.res_users: GC'd 1 user log entries 
2025-05-26 04:33:30,635 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.users.identitycheck()._transient_vacuum() 
2025-05-26 04:33:30,636 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling change.password.wizard()._transient_vacuum() 
2025-05-26 04:33:30,637 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling change.password.user()._transient_vacuum() 
2025-05-26 04:33:30,638 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling change.password.own()._transient_vacuum() 
2025-05-26 04:33:30,640 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.users.apikeys()._gc_user_apikeys() 
2025-05-26 04:33:30,641 9899 INFO 1006 odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-05-26 04:33:30,642 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.users.apikeys.description()._transient_vacuum() 
2025-05-26 04:33:30,644 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.device.log()._gc_device_log() 
2025-05-26 04:33:30,645 9899 INFO 1006 odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-05-26 04:33:30,646 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling res.device()._gc_device_log() 
2025-05-26 04:33:30,647 9899 INFO 1006 odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-05-26 04:33:30,648 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.module.update()._transient_vacuum() 
2025-05-26 04:33:30,649 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.language.install()._transient_vacuum() 
2025-05-26 04:33:30,650 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.language.import()._transient_vacuum() 
2025-05-26 04:33:30,651 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.module.upgrade()._transient_vacuum() 
2025-05-26 04:33:30,652 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.module.uninstall()._transient_vacuum() 
2025-05-26 04:33:30,654 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.language.export()._transient_vacuum() 
2025-05-26 04:33:30,656 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.partner.merge.line()._transient_vacuum() 
2025-05-26 04:33:30,658 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.partner.merge.automatic.wizard()._transient_vacuum() 
2025-05-26 04:33:30,660 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.document.layout()._transient_vacuum() 
2025-05-26 04:33:30,662 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling auth_totp.device()._gc_user_apikeys() 
2025-05-26 04:33:30,663 9899 INFO 1006 odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-05-26 04:33:30,663 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling auth_totp.wizard()._transient_vacuum() 
2025-05-26 04:33:30,666 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base_import.import()._transient_vacuum() 
2025-05-26 04:33:30,668 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.import.module()._transient_vacuum() 
2025-05-26 04:33:30,669 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling bus.bus()._gc_messages() 
2025-05-26 04:33:30,674 9899 INFO 1006 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659] 
2025-05-26 04:33:30,677 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling bus.presence()._gc_bus_presence() 
2025-05-26 04:33:30,691 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.activity()._gc_delete_old_overdue_activities() 
2025-05-26 04:33:30,696 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.link.preview()._gc_mail_link_preview() 
2025-05-26 04:33:30,698 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.message.translation()._gc_translations() 
2025-05-26 04:33:30,705 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling discuss.channel.member()._gc_unpin_outdated_sub_channels() 
2025-05-26 04:33:30,706 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling discuss.channel.rtc.session()._gc_inactive_sessions() 
2025-05-26 04:33:30,710 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.blacklist.remove()._transient_vacuum() 
2025-05-26 04:33:30,712 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.compose.message()._gc_lost_attachments() 
2025-05-26 04:33:30,713 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.compose.message()._transient_vacuum() 
2025-05-26 04:33:30,714 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.activity.schedule()._transient_vacuum() 
2025-05-26 04:33:30,715 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.resend.message()._transient_vacuum() 
2025-05-26 04:33:30,716 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.resend.partner()._transient_vacuum() 
2025-05-26 04:33:30,717 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.template.preview()._transient_vacuum() 
2025-05-26 04:33:30,717 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.template.reset()._transient_vacuum() 
2025-05-26 04:33:30,719 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling mail.wizard.invite()._transient_vacuum() 
2025-05-26 04:33:30,724 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.module.install.request()._transient_vacuum() 
2025-05-26 04:33:30,725 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling base.module.install.review()._transient_vacuum() 
2025-05-26 04:33:30,728 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling phone.blacklist.remove()._transient_vacuum() 
2025-05-26 04:33:30,729 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling privacy.lookup.wizard()._transient_vacuum() 
2025-05-26 04:33:30,730 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling privacy.lookup.wizard.line()._transient_vacuum() 
2025-05-26 04:33:30,740 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling product.label.layout()._transient_vacuum() 
2025-05-26 04:33:30,741 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling update.product.attribute.value()._transient_vacuum() 
2025-05-26 04:33:30,746 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling portal.share()._transient_vacuum() 
2025-05-26 04:33:30,747 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling portal.wizard()._transient_vacuum() 
2025-05-26 04:33:30,748 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling portal.wizard.user()._transient_vacuum() 
2025-05-26 04:33:30,749 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.sms()._gc_device() 
2025-05-26 04:33:30,750 9899 INFO 1006 odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-05-26 04:33:30,751 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.account.code()._transient_vacuum() 
2025-05-26 04:33:30,753 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.account.phone()._transient_vacuum() 
2025-05-26 04:33:30,753 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.account.sender()._transient_vacuum() 
2025-05-26 04:33:30,755 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.composer()._transient_vacuum() 
2025-05-26 04:33:30,756 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.resend.recipient()._transient_vacuum() 
2025-05-26 04:33:30,756 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.resend()._transient_vacuum() 
2025-05-26 04:33:30,757 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.template.preview()._transient_vacuum() 
2025-05-26 04:33:30,758 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sms.template.reset()._transient_vacuum() 
2025-05-26 04:33:30,759 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling snailmail.letter.format.error()._transient_vacuum() 
2025-05-26 04:33:30,760 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling snailmail.letter.missing.required.fields()._transient_vacuum() 
2025-05-26 04:33:30,763 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling payment.capture.wizard()._transient_vacuum() 
2025-05-26 04:33:30,764 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling payment.link.wizard()._transient_vacuum() 
2025-05-26 04:33:30,765 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling payment.provider.onboarding.wizard()._transient_vacuum() 
2025-05-26 04:33:30,784 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.automatic.entry.wizard()._transient_vacuum() 
2025-05-26 04:33:30,785 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.autopost.bills.wizard()._transient_vacuum() 
2025-05-26 04:33:30,786 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling validate.account.move()._transient_vacuum() 
2025-05-26 04:33:30,787 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.move.reversal()._transient_vacuum() 
2025-05-26 04:33:30,789 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.resequence.wizard()._transient_vacuum() 
2025-05-26 04:33:30,790 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.secure.entries.wizard()._transient_vacuum() 
2025-05-26 04:33:30,791 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.financial.year.op()._transient_vacuum() 
2025-05-26 04:33:30,792 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.setup.bank.manual.config()._transient_vacuum() 
2025-05-26 04:33:30,794 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.move.send.wizard()._transient_vacuum() 
2025-05-26 04:33:30,795 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.move.send.batch.wizard()._transient_vacuum() 
2025-05-26 04:33:30,796 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.payment.register()._transient_vacuum() 
2025-05-26 04:33:30,797 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.accrued.orders.wizard()._transient_vacuum() 
2025-05-26 04:33:30,799 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.merge.wizard()._transient_vacuum() 
2025-05-26 04:33:30,800 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.merge.wizard.line()._transient_vacuum() 
2025-05-26 04:33:30,807 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.departure.wizard()._transient_vacuum() 
2025-05-26 04:33:30,809 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling spreadsheet.revision()._gc_revisions() 
2025-05-26 04:33:30,815 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.warehouse.orderpoint()._unlink_processed_orderpoints() 
2025-05-26 04:33:30,823 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.traceability.report()._transient_vacuum() 
2025-05-26 04:33:30,825 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.return.picking.line()._transient_vacuum() 
2025-05-26 04:33:30,827 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.return.picking()._transient_vacuum() 
2025-05-26 04:33:30,828 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.change.product.qty()._transient_vacuum() 
2025-05-26 04:33:30,829 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.inventory.conflict()._transient_vacuum() 
2025-05-26 04:33:30,830 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.inventory.warning()._transient_vacuum() 
2025-05-26 04:33:30,832 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.inventory.adjustment.name()._transient_vacuum() 
2025-05-26 04:33:30,833 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling picking.label.type()._transient_vacuum() 
2025-05-26 04:33:30,834 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling lot.label.layout()._transient_vacuum() 
2025-05-26 04:33:30,835 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.backorder.confirmation.line()._transient_vacuum() 
2025-05-26 04:33:30,836 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.backorder.confirmation()._transient_vacuum() 
2025-05-26 04:33:30,837 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.quantity.history()._transient_vacuum() 
2025-05-26 04:33:30,838 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.rules.report()._transient_vacuum() 
2025-05-26 04:33:30,839 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.warn.insufficient.qty.scrap()._transient_vacuum() 
2025-05-26 04:33:30,841 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling product.replenish()._transient_vacuum() 
2025-05-26 04:33:30,842 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.track.confirmation()._transient_vacuum() 
2025-05-26 04:33:30,843 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.track.line()._transient_vacuum() 
2025-05-26 04:33:30,844 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.package.destination()._transient_vacuum() 
2025-05-26 04:33:30,845 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.orderpoint.snooze()._transient_vacuum() 
2025-05-26 04:33:30,846 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.request.count()._transient_vacuum() 
2025-05-26 04:33:30,847 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.replenishment.info()._transient_vacuum() 
2025-05-26 04:33:30,849 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.replenishment.option()._transient_vacuum() 
2025-05-26 04:33:30,850 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.quant.relocate()._transient_vacuum() 
2025-05-26 04:33:30,853 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling website.page.properties.base()._transient_vacuum() 
2025-05-26 04:33:30,854 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling website.page.properties()._transient_vacuum() 
2025-05-26 04:33:30,860 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling website.robots()._transient_vacuum() 
2025-05-26 04:33:30,861 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling website.custom_blocked_third_party_domains()._transient_vacuum() 
2025-05-26 04:33:30,863 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.change.lock.date()._transient_vacuum() 
2025-05-26 04:33:30,864 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.auto.reconcile.wizard()._transient_vacuum() 
2025-05-26 04:33:30,865 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.reconcile.wizard()._transient_vacuum() 
2025-05-26 04:33:30,867 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.batch.error.wizard()._transient_vacuum() 
2025-05-26 04:33:30,869 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.batch.error.wizard.line()._transient_vacuum() 
2025-05-26 04:33:30,875 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling payment.refund.wizard()._transient_vacuum() 
2025-05-26 04:33:30,878 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.expense.refuse.wizard()._transient_vacuum() 
2025-05-26 04:33:30,879 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.expense.approve.duplicate()._transient_vacuum() 
2025-05-26 04:33:30,880 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.expense.split.wizard()._transient_vacuum() 
2025-05-26 04:33:30,882 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.expense.split()._transient_vacuum() 
2025-05-26 04:33:30,885 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling hr.employee.cv.wizard()._transient_vacuum() 
2025-05-26 04:33:30,888 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling loyalty.card.update.balance()._transient_vacuum() 
2025-05-26 04:33:30,889 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling loyalty.generate.wizard()._transient_vacuum() 
2025-05-26 04:33:30,892 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling bill.to.po.wizard()._transient_vacuum() 
2025-05-26 04:33:30,894 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock.valuation.layer.revaluation()._transient_vacuum() 
2025-05-26 04:33:30,896 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling stock_barcode.cancel.operation()._transient_vacuum() 
2025-05-26 04:33:30,898 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling confirm.stock.sms()._transient_vacuum() 
2025-05-26 04:33:30,899 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.batch.payment.rejection()._transient_vacuum() 
2025-05-26 04:33:30,902 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling avatax.validate.address()._transient_vacuum() 
2025-05-26 04:33:30,903 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling avatax.connection.test.result()._transient_vacuum() 
2025-05-26 04:33:30,904 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.import.summary()._transient_vacuum() 
2025-05-26 04:33:30,906 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.bank.selection()._transient_vacuum() 
2025-05-26 04:33:30,907 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.missing.transaction.wizard()._transient_vacuum() 
2025-05-26 04:33:30,908 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.duplicate.transaction.wizard()._transient_vacuum() 
2025-05-26 04:33:30,909 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.bank.statement.line.transient()._transient_vacuum() 
2025-05-26 04:33:30,911 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling l10n_us_1099.wizard()._transient_vacuum() 
2025-05-26 04:33:30,919 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling pos.details.wizard()._transient_vacuum() 
2025-05-26 04:33:30,921 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling pos.make.payment()._transient_vacuum() 
2025-05-26 04:33:30,922 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling pos.close.session.wizard()._transient_vacuum() 
2025-05-26 04:33:30,923 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling pos.daily.sales.reports.wizard()._transient_vacuum() 
2025-05-26 04:33:30,928 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.mass.cancel.orders()._transient_vacuum() 
2025-05-26 04:33:30,929 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.payment.provider.onboarding.wizard()._transient_vacuum() 
2025-05-26 04:33:30,930 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.advance.payment.inv()._transient_vacuum() 
2025-05-26 04:33:30,932 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.order.cancel()._transient_vacuum() 
2025-05-26 04:33:30,933 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.order.discount()._transient_vacuum() 
2025-05-26 04:33:30,943 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.report.send()._transient_vacuum() 
2025-05-26 04:33:30,944 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.report.file.download.error.wizard()._transient_vacuum() 
2025-05-26 04:33:30,945 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.multicurrency.revaluation.wizard()._transient_vacuum() 
2025-05-26 04:33:30,947 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account_reports.export.wizard()._transient_vacuum() 
2025-05-26 04:33:30,948 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account_reports.export.wizard.format()._transient_vacuum() 
2025-05-26 04:33:30,949 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling choose.delivery.carrier()._transient_vacuum() 
2025-05-26 04:33:30,951 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling expense.sample.register()._transient_vacuum() 
2025-05-26 04:33:30,954 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling pos_preparation_display.reset.wizard()._transient_vacuum() 
2025-05-26 04:33:30,956 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.loyalty.coupon.wizard()._transient_vacuum() 
2025-05-26 04:33:30,957 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.loyalty.reward.wizard()._transient_vacuum() 
2025-05-26 04:33:30,961 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling asset.modify()._transient_vacuum() 
2025-05-26 04:33:30,963 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account_followup.manual_reminder()._transient_vacuum() 
2025-05-26 04:33:30,964 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account_followup.missing.information.wizard()._transient_vacuum() 
2025-05-26 04:33:30,968 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling sale.commission.plan.user.wizard()._transient_vacuum() 
2025-05-26 04:33:30,970 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling choose.delivery.package()._transient_vacuum() 
2025-05-26 04:33:30,973 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.loan.compute.wizard()._transient_vacuum() 
2025-05-26 04:33:30,974 9899 DEBUG 1006 odoo.addons.base.models.ir_autovacuum: Calling account.loan.close.wizard()._transient_vacuum() 
2025-05-26 04:33:30,975 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.400s 
2025-05-26 04:33:30,975 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) server action #35 with uid 1 executed in 0.400s 
2025-05-26 04:33:31,001 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-05-26 04:33:31,003 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-05-26 04:33:31,004 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 1 updated and released 
2025-05-26 04:33:31,005 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 27 acquired 
2025-05-26 04:33:31,009 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: cron.object.execute('1006', 1, '*', 'Account: Journal online Waiting Synchronization', 509) 
2025-05-26 04:33:31,009 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) starting 
2025-05-26 04:33:31,013 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) done in 0.003s 
2025-05-26 04:33:31,013 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) server action #509 with uid 1 executed in 0.003s 
2025-05-26 04:33:31,015 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) processed 0 records, 0 records remaining 
2025-05-26 04:33:31,016 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) completed 
2025-05-26 04:33:31,017 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 27 updated and released 
2025-05-26 04:33:34,331 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:34:31,110 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:34:33,777 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:35:29,502 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:35:34,037 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:36:27,771 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:36:33,328 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:36:35,583 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:36:35,584 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:35] "GET / HTTP/1.1" 303 - 8 0.004 0.006
2025-05-26 04:36:35,600 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:35,606 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar/' (lang: 'ar') homepage with trailing slash, redirect 
2025-05-26 04:36:35,606 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:35] "GET /ar/ HTTP/1.1" 301 - 2 0.001 0.018
2025-05-26 04:36:35,840 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:35,846 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar' (lang: 'ar') valid lang in url, rewrite url and continue 
2025-05-26 04:36:35,850 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:36,515 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:36] "GET /ar HTTP/1.1" 200 - 136 0.054 0.621
2025-05-26 04:36:36,849 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:36,851 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:36,856 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:36] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 1 0.001 0.004
2025-05-26 04:36:37,483 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:37,495 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:37] "GET /web/assets/1/5c26f90/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 8 0.004 0.008
2025-05-26 04:36:37,873 9899 WARNING 1006 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-05-26 04:36:37,939 9899 INFO 1006 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/a21a1de/web.assets_frontend.rtl.min.css (id:1661) 
2025-05-26 04:36:37,939 9899 INFO 1006 odoo.addons.base.models.assetsbundle: Deleting attachments [1631] (matching /web/assets/1/_______/web.assets_frontend.rtl.min.css) because it was replaced with /web/assets/1/a21a1de/web.assets_frontend.rtl.min.css 
2025-05-26 04:36:37,943 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:37] "GET /web/assets/1/a21a1de/web.assets_frontend.rtl.min.css HTTP/1.1" 200 - 15 0.018 1.077
2025-05-26 04:36:38,282 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:38,296 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:38] "GET /web/image/website/1/logo/My%20Website?unique=e7058f5 HTTP/1.1" 200 - 9 0.005 0.009
2025-05-26 04:36:38,611 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:38,613 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:38,615 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:38,645 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:38] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 1 0.001 0.029
2025-05-26 04:36:38,646 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:38] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 1 0.001 0.034
2025-05-26 04:36:38,646 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:38] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 1 0.001 0.032
2025-05-26 04:36:39,132 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:39,144 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:39,149 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:39] "GET /web/image/website/1/favicon?unique=e7058f5 HTTP/1.1" 200 - 9 0.003 0.014
2025-05-26 04:36:39,153 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:39] "GET /web/assets/1/bab6096/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 8 0.003 0.006
2025-05-26 04:36:39,414 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:39,418 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:36:39,419 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:39] "GET /website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd?lang=ar_001 HTTP/1.1" 303 - 2 0.001 0.004
2025-05-26 04:36:39,581 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:39,586 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar/website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd' (lang: 'ar') valid lang in url, rewrite url and continue 
2025-05-26 04:36:39,590 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:39,602 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:39] "GET /ar/website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd?lang=ar_001 HTTP/1.1" 200 - 5 0.003 0.018
2025-05-26 04:36:40,351 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:40,356 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/web/login' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:36:40,357 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:40] "GET /web/login HTTP/1.1" 303 - 2 0.001 0.005
2025-05-26 04:36:40,688 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:40,693 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar/web/login' (lang: 'ar') valid lang in url, rewrite url and continue 
2025-05-26 04:36:40,698 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:40,728 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:40] "GET /ar/web/login HTTP/1.1" 200 - 19 0.008 0.032
2025-05-26 04:36:41,140 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:41,146 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:36:41,147 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:41] "GET /website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd?lang=ar_001 HTTP/1.1" 303 - 2 0.002 0.006
2025-05-26 04:36:42,659 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:42,665 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/web/login' (lang: 'ar') no lang in url and should not redirect (e.g. POST), continue 
2025-05-26 04:36:42,669 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:42,945 9899 INFO 1006 odoo.addons.base.models.res_users: Login failed for db:1006 login:admin from 127.0.0.1 
2025-05-26 04:36:43,030 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:43] "POST /web/login HTTP/1.1" 200 - 17 0.008 0.363
2025-05-26 04:36:43,122 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:43,128 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:36:43,129 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:43] "GET /website/translations/e443a85a7dfbbf8fb7b1c2eb9d6ea26a0808ddbd?lang=ar_001 HTTP/1.1" 303 - 2 0.002 0.005
2025-05-26 04:36:51,737 9899 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "/odoo/180/odoo/tools/_vendor/sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/root/.local/share/Odoo/sessions/Ov/OvZGVl4t67sSJj_amVjeIWSAXBBBr56LlvmHVhDf6cMM7C_rityqMAy-xTsJP0sPg3RowzpUDwB9k0HIUKqN'
2025-05-26 04:36:51,742 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/web/login' (lang: 'ar') no lang in url and should not redirect (e.g. POST), continue 
2025-05-26 04:36:51,747 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:52,050 9899 INFO 1006 odoo.addons.base.models.res_users: Login successful for db:1006 login:admin from 127.0.0.1 
2025-05-26 04:36:52,062 9899 INFO 1006 odoo.models.unlink: User #4 deleted website.visitor records with IDs: [12] 
2025-05-26 04:36:52,069 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "POST /web/login HTTP/1.1" 303 - 39 0.019 0.314
2025-05-26 04:36:52,080 9899 DEBUG 1006 odoo.http: Couldn't load Geoip Country file ([Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'). Fallbacks on Geoip City. 
2025-05-26 04:36:52,080 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 1203, in _country_record
    return root.geoip_country_db.country(self.ip)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 42, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2326, in geoip_country_db
    return geoip2.database.Reader(config['geoip_country_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:36:52,081 9899 INFO 1006 odoo.addons.base.models.res_device: User 2 inserts device log (UsY_ObYTaMfcuMMrj3P9Wm85KaHDtM0xCA-x280wOT) 
2025-05-26 04:36:52,133 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /odoo HTTP/1.1" 200 - 42 0.016 0.039
2025-05-26 04:36:52,151 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /web/assets/e544221/web.assets_web.min.css HTTP/1.1" 200 - 3 0.002 0.004
2025-05-26 04:36:52,469 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /web/webclient/translations/7d265c1ef0e08514e4c0d3a46dd290e842665100?lang=en_US HTTP/1.1" 200 - 1 0.001 0.005
2025-05-26 04:36:52,493 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /web/assets/f7b9eb5/web.assets_web.min.js HTTP/1.1" 200 - 3 0.002 0.032
2025-05-26 04:36:52,494 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /web/assets/078b6a1/web.assets_web_print.min.css HTTP/1.1" 200 - 3 0.002 0.020
2025-05-26 04:36:52,587 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:52] "GET /web/webclient/load_menus/16d607722abf143f4af6f5bae14e49a743d3241a7e82a256e30a24aa77dc654a HTTP/1.1" 200 - 190 0.078 0.051
2025-05-26 04:36:53,030 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "POST /mail/data HTTP/1.1" 200 - 22 0.008 0.009
2025-05-26 04:36:53,108 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.002
2025-05-26 04:36:53,185 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.009 0.006
2025-05-26 04:36:53,189 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 9 0.010 0.011
2025-05-26 04:36:53,191 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web/image/res.partner/3/avatar_128?unique=1747724583000 HTTP/1.1" 200 - 8 0.010 0.012
2025-05-26 04:36:53,195 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web_enterprise/static/img/background-light.svg HTTP/1.1" 200 - 0 0.000 0.002
2025-05-26 04:36:53,375 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.024
2025-05-26 04:36:53,530 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 200 - 3 0.001 0.026
2025-05-26 04:36:53,851 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:53] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.003
2025-05-26 04:36:54,784 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:36:54] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.004
2025-05-26 04:37:02,032 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:02] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.006
2025-05-26 04:37:02,293 9899 DEBUG 1006 odoo.api: call pos.config().get_views(options={'action_id': 544, 'embedded_action_id': False, 'embedded_parent_res_id': False, 'load_filters': True, 'toolbar': True}, views=[[False, 'kanban'], [False, 'list'], [False, 'form'], [1488, 'search']]) 
2025-05-26 04:37:02,301 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:02] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 3 0.002 0.009
2025-05-26 04:37:02,354 9899 DEBUG 1006 odoo.api: call pos.config().web_search_read(count_limit=10001, domain=[], limit=80, offset=0, order='', specification={'cash_control': {}, 'current_session_id': {'fields': {'display_name': {}}}, 'current_session_state': {}, 'access_token': {}, 'pos_session_state': {}, 'pos_session_duration': {}, 'currency_id': {'fields': {'display_name': {}}}, 'name': {}, 'pos_session_username': {}, 'last_session_closing_date': {}, 'last_session_closing_cash': {}, 'number_of_rescue_session': {}, 'current_user_id': {'fields': {'display_name': {}}}, 'customer_display_type': {}}) 
2025-05-26 04:37:02,365 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:02] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 13 0.005 0.009
2025-05-26 04:37:02,633 9899 DEBUG 1006 odoo.api: call pos.config().get_pos_kanban_view_state() 
2025-05-26 04:37:02,637 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:02] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 5 0.002 0.004
2025-05-26 04:37:02,695 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:02] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 9 0.003 0.010
2025-05-26 04:37:04,041 9899 DEBUG 1006 odoo.api: call pos.config(1,).open_ui() 
2025-05-26 04:37:04,056 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:04] "POST /web/dataset/call_button/pos.config/open_ui HTTP/1.1" 200 - 19 0.008 0.011
2025-05-26 04:37:04,371 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:04] "GET /pos/ui?config_id=1&from_backend=True HTTP/1.1" 200 - 31 0.012 0.041
2025-05-26 04:37:04,401 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:04] "GET /web/assets/f0ab9b8/point_of_sale.assets_prod.min.css HTTP/1.1" 200 - 3 0.001 0.004
2025-05-26 04:37:04,738 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:04] "GET /web/assets/38aac51/point_of_sale.assets_prod.min.js HTTP/1.1" 200 - 3 0.001 0.005
2025-05-26 04:37:05,189 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.027
2025-05-26 04:37:05,198 9899 DEBUG 1006 odoo.api: call pos.session(22,).load_data([]) 
2025-05-26 04:37:05,201 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 3 0.002 0.005
2025-05-26 04:37:05,454 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/favicon.ico' (lang: 'ar') no lang in url and should not redirect (e.g. POST), continue 
2025-05-26 04:37:05,459 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "GET /favicon.ico HTTP/1.1" 301 - 2 0.003 0.007
2025-05-26 04:37:05,510 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "POST /web/dataset/call_kw/pos.session/load_data HTTP/1.1" 200 - 169 0.082 0.236
2025-05-26 04:37:05,547 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.001 0.003
2025-05-26 04:37:05,563 9899 DEBUG 1006 odoo.api: call barcode.nomenclature(1,).read(['name', 'rule_ids', 'upc_ean_conv', 'is_gs1_nomenclature', 'gs1_separator_fnc1']) 
2025-05-26 04:37:05,565 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "POST /web/dataset/call_kw/barcode.nomenclature/read HTTP/1.1" 200 - 3 0.001 0.003
2025-05-26 04:37:05,889 9899 DEBUG 1006 odoo.api: call barcode.rule().search_read(domain=[['barcode_nomenclature_id', '=', 1]], fields=['name', 'sequence', 'type', 'encoding', 'pattern', 'alias', 'gs1_content_type', 'gs1_decimal_usage', 'associated_uom_id']) 
2025-05-26 04:37:05,890 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:05] "POST /web/dataset/call_kw/barcode.rule/search_read HTTP/1.1" 200 - 2 0.001 0.003
2025-05-26 04:37:06,138 9899 DEBUG 1006 odoo.api: call pos.config(1,).fetch_multi_currency_data() 
2025-05-26 04:37:06,144 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "POST /web/dataset/call_kw/pos.config/fetch_multi_currency_data HTTP/1.1" 200 - 7 0.003 0.006
2025-05-26 04:37:06,216 9899 DEBUG 1006 odoo.api: call pos.config(1,).read_config_open_orders({'pos.order': ['&', '&', ['id', 'not in', []], ['state', '=', 'draft'], ['config_id', 'in', [1]]]}, {'pos.order': []}) 
2025-05-26 04:37:06,218 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 2 0.001 0.004
2025-05-26 04:37:06,500 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "GET /point_of_sale/static/src/fonts/Inconsolata.otf HTTP/1.1" 200 - 0 0.000 0.024
2025-05-26 04:37:06,541 9899 DEBUG 1006 odoo.api: call res.partner(13, 7, 3, 10, 11, 12, 9, 1).get_all_total_due(74) 
2025-05-26 04:37:06,559 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "GET /web/static/img/logo.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-26 04:37:06,569 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "GET /hr_attendance/static/img/background-light.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-05-26 04:37:06,570 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "POST /web/dataset/call_kw/res.partner/get_all_total_due HTTP/1.1" 200 - 21 0.020 0.013
2025-05-26 04:37:06,573 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:06] "GET /web/image?model=res.company&id=1&field=logo HTTP/1.1" 200 - 5 0.008 0.012
2025-05-26 04:37:10,432 9899 DEBUG 1006 odoo.api: call pos.session(22,).write({'employee_id': 1}) 
2025-05-26 04:37:10,461 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:10] "POST /web/dataset/call_kw/pos.session/write HTTP/1.1" 200 - 5 0.003 0.029
2025-05-26 04:37:10,779 9899 DEBUG 1006 odoo.api: call pos.config(1,).notify_synchronisation(22, '3', {'pos.session': [22]}) 
2025-05-26 04:37:10,788 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:10] "GET /web/image/hr.employee.public/1/avatar_128 HTTP/1.1" 200 - 6 0.004 0.008
2025-05-26 04:37:10,800 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:10] "GET /web/image?model=product.template&field=image_128&id=10&unique=2025-05-20%2003:04:15 HTTP/1.1" 200 - 6 0.004 0.006
2025-05-26 04:37:10,818 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:10] "POST /web/dataset/call_kw/pos.config/notify_synchronisation HTTP/1.1" 200 - 9 0.007 0.036
2025-05-26 04:37:11,172 9899 DEBUG 1006 odoo.api: call pos.config(1,).read_config_open_orders({'pos.order': ['&', '&', ['id', 'not in', []], ['state', '=', 'draft'], ['config_id', 'in', [1]]]}, {'pos.order': []}) 
2025-05-26 04:37:11,174 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:11] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 2 0.001 0.004
2025-05-26 04:37:17,139 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:17] "GET /web/static/fonts/google/Roboto/Roboto-Regular.ttf HTTP/1.1" 200 - 0 0.000 0.002
2025-05-26 04:37:26,299 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:37:32,838 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:37:40,828 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:40] "GET /point_of_sale/static/src/img/money.png HTTP/1.1" 200 - 0 0.000 0.025
2025-05-26 04:37:41,115 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:41] "GET /point_of_sale/static/src/img/card-bank.png HTTP/1.1" 200 - 0 0.000 0.002
2025-05-26 04:37:41,115 9899 INFO ? werkzeug: 127.0.0.1 - - [26/May/2025 04:37:41] "GET /point_of_sale/static/src/img/pay-later.png HTTP/1.1" 200 - 0 0.000 0.001
2025-05-26 04:37:50,870 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/.well-known/appspecific/com.chrome.devtools.json' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:37:50,870 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:50] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 303 - 1 0.001 0.002
2025-05-26 04:37:51,120 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar/.well-known/appspecific/com.chrome.devtools.json' (lang: 'ar') valid lang in url, rewrite url and continue 
2025-05-26 04:37:51,229 9899 DEBUG 1006 odoo.http: Couldn't load Geoip Country file ([Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'). Fallbacks on Geoip City. 
2025-05-26 04:37:51,229 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 1203, in _country_record
    return root.geoip_country_db.country(self.ip)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 42, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2326, in geoip_country_db
    return geoip2.database.Reader(config['geoip_country_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:37:51,337 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:37:51] "GET /ar/.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 120 0.065 0.154
2025-05-26 04:38:00,158 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:38:00] "GET /web/image/hr.employee.public/1/avatar_128 HTTP/1.1" 304 - 6 0.003 0.008
2025-05-26 04:38:25,593 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:38:25,595 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 27 acquired 
2025-05-26 04:38:25,625 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: cron.object.execute('1006', 1, '*', 'Account: Journal online Waiting Synchronization', 509) 
2025-05-26 04:38:25,625 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) starting 
2025-05-26 04:38:25,628 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) done in 0.003s 
2025-05-26 04:38:25,628 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) server action #509 with uid 1 executed in 0.003s 
2025-05-26 04:38:25,632 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) processed 0 records, 0 records remaining 
2025-05-26 04:38:25,634 9899 INFO 1006 odoo.addons.base.models.ir_cron: Job 'Account: Journal online Waiting Synchronization' (27) completed 
2025-05-26 04:38:25,636 9899 DEBUG 1006 odoo.addons.base.models.ir_cron: job 27 updated and released 
2025-05-26 04:38:32,398 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:39:24,248 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:39:31,974 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:39:31,978 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 8 acquired 
2025-05-26 04:39:32,031 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: cron.object.execute('1001', 1, '*', 'Notification: Notify scheduled messages', 138) 
2025-05-26 04:39:32,031 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-05-26 04:39:32,037 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.005s 
2025-05-26 04:39:32,037 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) server action #138 with uid 1 executed in 0.005s 
2025-05-26 04:39:32,040 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-05-26 04:39:32,047 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-05-26 04:39:32,050 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 8 updated and released 
2025-05-26 04:39:32,051 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 3 acquired 
2025-05-26 04:39:32,054 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: cron.object.execute('1001', 1, '*', 'Mail: Email Queue Manager', 133) 
2025-05-26 04:39:32,055 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-05-26 04:39:32,058 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.003s 
2025-05-26 04:39:32,058 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) server action #133 with uid 1 executed in 0.003s 
2025-05-26 04:39:32,061 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-05-26 04:39:32,063 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-05-26 04:39:32,065 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 3 updated and released 
2025-05-26 04:40:09,876 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:09] "GET /pos/ui?config_id=1&from_backend=True HTTP/1.1" 200 - 31 0.010 0.016
2025-05-26 04:40:10,303 9899 DEBUG 1006 odoo.api: call pos.session(22,).load_data([]) 
2025-05-26 04:40:10,729 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:10] "POST /web/dataset/call_kw/pos.session/load_data HTTP/1.1" 200 - 169 0.069 0.360
2025-05-26 04:40:10,787 9899 DEBUG 1006 odoo.api: call barcode.nomenclature(1,).read(['name', 'rule_ids', 'upc_ean_conv', 'is_gs1_nomenclature', 'gs1_separator_fnc1']) 
2025-05-26 04:40:10,789 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:10] "POST /web/dataset/call_kw/barcode.nomenclature/read HTTP/1.1" 200 - 3 0.001 0.004
2025-05-26 04:40:11,109 9899 DEBUG 1006 odoo.api: call barcode.rule().search_read(domain=[['barcode_nomenclature_id', '=', 1]], fields=['name', 'sequence', 'type', 'encoding', 'pattern', 'alias', 'gs1_content_type', 'gs1_decimal_usage', 'associated_uom_id']) 
2025-05-26 04:40:11,110 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "POST /web/dataset/call_kw/barcode.rule/search_read HTTP/1.1" 200 - 2 0.001 0.003
2025-05-26 04:40:11,379 9899 DEBUG 1006 odoo.api: call pos.config(1,).fetch_multi_currency_data() 
2025-05-26 04:40:11,385 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "POST /web/dataset/call_kw/pos.config/fetch_multi_currency_data HTTP/1.1" 200 - 7 0.002 0.006
2025-05-26 04:40:11,442 9899 DEBUG 1006 odoo.api: call pos.config(1,).read_config_open_orders({'pos.order': ['&', '&', ['id', 'not in', []], ['state', '=', 'draft'], ['config_id', 'in', [1]]]}, {'pos.order': []}) 
2025-05-26 04:40:11,444 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 2 0.001 0.003
2025-05-26 04:40:11,764 9899 DEBUG 1006 odoo.api: call res.partner(13, 7, 3, 10, 11, 12, 9, 1).get_all_total_due(74) 
2025-05-26 04:40:11,778 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "GET /odoo/action-point_of_sale.action_client_pos_menu HTTP/1.1" 200 - 21 0.007 0.040
2025-05-26 04:40:11,784 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "POST /web/dataset/call_kw/res.partner/get_all_total_due HTTP/1.1" 200 - 21 0.011 0.012
2025-05-26 04:40:11,820 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "GET /web/image?model=res.company&id=1&field=logo HTTP/1.1" 304 - 5 0.003 0.010
2025-05-26 04:40:11,864 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/.well-known/appspecific/com.chrome.devtools.json' (lang: 'ar') missing lang in url, redirect 
2025-05-26 04:40:11,865 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:11] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 303 - 1 0.001 0.003
2025-05-26 04:40:12,186 9899 DEBUG 1006 odoo.addons.http_routing.models.ir_http: '/ar/.well-known/appspecific/com.chrome.devtools.json' (lang: 'ar') valid lang in url, rewrite url and continue 
2025-05-26 04:40:12,209 9899 DEBUG 1006 odoo.http: Couldn't load Geoip Country file ([Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'). Fallbacks on Geoip City. 
2025-05-26 04:40:12,210 9899 DEBUG 1006 odoo.http: Couldn't load Geoip City file at /usr/share/GeoIP/GeoLite2-City.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 1203, in _country_record
    return root.geoip_country_db.country(self.ip)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/odoo/tools/func.py", line 42, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "/odoo/180/odoo/http.py", line 2326, in geoip_country_db
    return geoip2.database.Reader(config['geoip_country_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-Country.mmdb'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/odoo/180/odoo/http.py", line 2315, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/geoip2/database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/odoo/180/venv/lib/python3.12/site-packages/maxminddb/__init__.py", line 83, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'/usr/share/GeoIP/GeoLite2-City.mmdb'
2025-05-26 04:40:12,219 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:12] "GET /ar/.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 12 0.006 0.030
2025-05-26 04:40:12,469 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:12] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.003 0.005
2025-05-26 04:40:13,774 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:13] "POST /mail/data HTTP/1.1" 200 - 22 0.008 0.009
2025-05-26 04:40:14,101 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:14] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.003
2025-05-26 04:40:16,474 9899 DEBUG 1006 odoo.api: call pos.session(22,).write({'employee_id': 1}) 
2025-05-26 04:40:16,493 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:16] "POST /web/dataset/call_kw/pos.session/write HTTP/1.1" 200 - 5 0.002 0.021
2025-05-26 04:40:16,495 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:16] "GET /web/image/hr.employee.public/1/avatar_128 HTTP/1.1" 304 - 6 0.003 0.006
2025-05-26 04:40:16,728 9899 DEBUG 1006 odoo.api: call pos.config(1,).notify_synchronisation(22, '4', {'pos.session': [22]}) 
2025-05-26 04:40:16,761 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:16] "POST /web/dataset/call_kw/pos.config/notify_synchronisation HTTP/1.1" 200 - 9 0.004 0.032
2025-05-26 04:40:17,103 9899 DEBUG 1006 odoo.api: call pos.config(1,).read_config_open_orders({'pos.order': ['&', '&', ['id', 'not in', []], ['state', '=', 'draft'], ['config_id', 'in', [1]]]}, {'pos.order': []}) 
2025-05-26 04:40:17,105 9899 INFO 1006 werkzeug: 127.0.0.1 - - [26/May/2025 04:40:17] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 2 0.001 0.005
2025-05-26 04:40:22,766 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:40:22,770 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 14 acquired 
2025-05-26 04:40:22,798 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: cron.object.execute('1001', 1, '*', 'SMS: SMS Queue Manager', 206) 
2025-05-26 04:40:22,798 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-05-26 04:40:22,801 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.003s 
2025-05-26 04:40:22,801 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) server action #206 with uid 1 executed in 0.003s 
2025-05-26 04:40:22,803 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-05-26 04:40:22,805 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-05-26 04:40:22,807 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 14 updated and released 
2025-05-26 04:40:22,807 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 15 acquired 
2025-05-26 04:40:22,811 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: cron.object.execute('1001', 1, '*', 'Snailmail: process letters queue', 216) 
2025-05-26 04:40:22,811 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-05-26 04:40:22,815 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.004s 
2025-05-26 04:40:22,815 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) server action #216 with uid 1 executed in 0.004s 
2025-05-26 04:40:22,819 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-05-26 04:40:22,820 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-05-26 04:40:22,822 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 15 updated and released 
2025-05-26 04:40:22,823 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 13 acquired 
2025-05-26 04:40:22,827 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: cron.object.execute('1001', 1, '*', 'Partner Autocomplete: Sync with remote DB', 202) 
2025-05-26 04:40:22,827 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-05-26 04:40:22,829 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.002s 
2025-05-26 04:40:22,829 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) server action #202 with uid 1 executed in 0.002s 
2025-05-26 04:40:22,832 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-05-26 04:40:22,834 9899 INFO 1001 odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-05-26 04:40:22,836 9899 DEBUG 1001 odoo.addons.base.models.ir_cron: job 13 updated and released 
2025-05-26 04:40:32,353 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-05-26 04:41:21,418 9899 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-05-26 04:41:31,951 9899 DEBUG ? odoo.service.server: cron1 polling for jobs 
