# -*- coding: utf-8 -*-
# Powered by Kanak Infosystems LLP.
# © 2020 Kanak Infosystems LLP. (<https://www.kanakinfosystems.com>).

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    receipt_size = fields.Selection([
        ('58mm', '58mm - Compact Receipt'),
        ('80mm', '80mm - Standard Receipt'),
    ], string='Receipt Size', default='80mm', 
       help='Select the receipt size for this POS. This will determine the layout and formatting of printed receipts.')

    receipt_show_client = fields.Boolean(
        string='Show Client Name on Receipt',
        default=True,
        help='Display client name on the receipt when a customer is selected.'
    )

    receipt_show_cashier = fields.Boolean(
        string='Show Cashier Name on Receipt',
        default=True,
        help='Display cashier name on the receipt.'
    )

    receipt_show_date_time = fields.Boolean(
        string='Show Date & Time on Receipt',
        default=True,
        help='Display date and time on the receipt.'
    )

    receipt_custom_header = fields.Text(
        string='Custom Receipt Header',
        help='Custom text to display at the top of the receipt (optional).'
    )

    receipt_custom_footer = fields.Text(
        string='Custom Receipt Footer',
        help='Custom text to display at the bottom of the receipt (optional).'
    )

    @api.model
    def get_receipt_config(self, config_id):
        """Get receipt configuration for frontend"""
        config = self.browse(config_id)
        return {
            'receipt_size': config.receipt_size,
            'receipt_show_client': config.receipt_show_client,
            'receipt_show_cashier': config.receipt_show_cashier,
            'receipt_show_date_time': config.receipt_show_date_time,
            'receipt_custom_header': config.receipt_custom_header,
            'receipt_custom_footer': config.receipt_custom_footer,
        }
