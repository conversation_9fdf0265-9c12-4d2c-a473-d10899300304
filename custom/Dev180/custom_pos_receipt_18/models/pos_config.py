# -*- coding: utf-8 -*-
# Powered by Kanak Infosystems LLP.
# © 2020 Kanak Infosystems LLP. (<https://www.kanakinfosystems.com>).

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    receipt_size = fields.Selection([
        ('58mm', '58mm - Compact Receipt'),
        ('80mm', '80mm - Standard Receipt'),
        ('custom', 'Custom Size'),
    ], string='Receipt Size', default='80mm',
       help='Select the receipt size for this POS. This will determine the layout and formatting of printed receipts.')

    # Custom Receipt Dimensions
    receipt_width = fields.Float(
        string='Receipt Width (mm)',
        default=80.0,
        help='Width of the receipt in millimeters. Standard sizes: 58mm, 80mm'
    )

    # Margins
    receipt_margin_top = fields.Float(
        string='Top Margin (mm)',
        default=0.0,
        help='Top margin of the receipt in millimeters'
    )

    receipt_margin_bottom = fields.Float(
        string='Bottom Margin (mm)',
        default=0.0,
        help='Bottom margin of the receipt in millimeters'
    )

    receipt_margin_left = fields.Float(
        string='Left Margin (mm)',
        default=0.0,
        help='Left margin of the receipt in millimeters'
    )

    receipt_margin_right = fields.Float(
        string='Right Margin (mm)',
        default=0.0,
        help='Right margin of the receipt in millimeters'
    )

    # Font Settings
    receipt_font_size = fields.Float(
        string='Font Size (px)',
        default=12.0,
        help='Font size for receipt text in pixels'
    )

    receipt_font_size_header = fields.Float(
        string='Header Font Size (px)',
        default=16.0,
        help='Font size for receipt header text in pixels'
    )

    receipt_font_size_small = fields.Float(
        string='Small Text Font Size (px)',
        default=10.0,
        help='Font size for small text (contact info, etc.) in pixels'
    )

    receipt_line_height = fields.Float(
        string='Line Height',
        default=1.3,
        help='Line height multiplier for text spacing'
    )

    # Display Options
    receipt_show_client = fields.Boolean(
        string='Show Client Name on Receipt',
        default=True,
        help='Display client name on the receipt when a customer is selected.'
    )

    receipt_show_cashier = fields.Boolean(
        string='Show Cashier Name on Receipt',
        default=True,
        help='Display cashier name on the receipt.'
    )

    receipt_show_date_time = fields.Boolean(
        string='Show Date & Time on Receipt',
        default=True,
        help='Display date and time on the receipt.'
    )

    receipt_custom_header = fields.Text(
        string='Custom Receipt Header',
        help='Custom text to display at the top of the receipt (optional).'
    )

    receipt_custom_footer = fields.Text(
        string='Custom Receipt Footer',
        help='Custom text to display at the bottom of the receipt (optional).'
    )

    @api.model
    def get_receipt_config(self, config_id):
        """Get receipt configuration for frontend"""
        config = self.browse(config_id)
        return {
            'receipt_size': config.receipt_size,
            'receipt_width': config.receipt_width,
            'receipt_margin_top': config.receipt_margin_top,
            'receipt_margin_bottom': config.receipt_margin_bottom,
            'receipt_margin_left': config.receipt_margin_left,
            'receipt_margin_right': config.receipt_margin_right,
            'receipt_font_size': config.receipt_font_size,
            'receipt_font_size_header': config.receipt_font_size_header,
            'receipt_font_size_small': config.receipt_font_size_small,
            'receipt_line_height': config.receipt_line_height,
            'receipt_show_client': config.receipt_show_client,
            'receipt_show_cashier': config.receipt_show_cashier,
            'receipt_show_date_time': config.receipt_show_date_time,
            'receipt_custom_header': config.receipt_custom_header,
            'receipt_custom_footer': config.receipt_custom_footer,
        }

    @api.onchange('receipt_size')
    def _onchange_receipt_size(self):
        """Auto-set dimensions based on receipt size"""
        if self.receipt_size == '58mm':
            self.receipt_width = 58.0
            self.receipt_font_size = 9.0
            self.receipt_font_size_header = 12.0
            self.receipt_font_size_small = 8.0
            self.receipt_line_height = 1.1
        elif self.receipt_size == '80mm':
            self.receipt_width = 80.0
            self.receipt_font_size = 12.0
            self.receipt_font_size_header = 16.0
            self.receipt_font_size_small = 10.0
            self.receipt_line_height = 1.3
