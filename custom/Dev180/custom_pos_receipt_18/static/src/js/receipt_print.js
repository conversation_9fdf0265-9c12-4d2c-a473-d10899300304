/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

patch(ReceiptScreen.prototype, {
    /**
     * Setup print styles for specific receipt size
     */
    setupPrintStyles(receiptSize) {
        // Remove existing print style if any
        const existingStyle = document.getElementById('pos-receipt-print-style');
        if (existingStyle) {
            existingStyle.remove();
        }

        // Create new print style
        const printStyle = document.createElement('style');
        printStyle.id = 'pos-receipt-print-style';
        printStyle.type = 'text/css';

        let cssRules = '';

        if (receiptSize === '58mm') {
            cssRules = `
                @media print {
                    @page {
                        size: 58mm auto;
                        margin: 0;
                        padding: 0;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        width: 58mm;
                    }

                    .pos-receipt {
                        width: 58mm !important;
                        max-width: 58mm !important;
                        min-width: 58mm !important;
                        margin: 0 !important;
                        padding: 2mm !important;
                        font-size: 9px !important;
                        line-height: 1.1 !important;
                    }
                }
            `;
        } else if (receiptSize === '80mm') {
            cssRules = `
                @media print {
                    @page {
                        size: 80mm auto;
                        margin: 0;
                        padding: 0;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        width: 80mm;
                    }

                    .pos-receipt {
                        width: 80mm !important;
                        max-width: 80mm !important;
                        min-width: 80mm !important;
                        margin: 0 !important;
                        padding: 3mm !important;
                        font-size: 11px !important;
                        line-height: 1.3 !important;
                    }
                }
            `;
        }

        printStyle.innerHTML = cssRules;
        document.head.appendChild(printStyle);

        console.log(`Print styles set for ${receiptSize} receipt`);
    }
});
