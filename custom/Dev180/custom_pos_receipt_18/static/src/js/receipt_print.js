/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

patch(ReceiptScreen.prototype, {
    /**
     * Setup print styles for specific receipt configuration
     */
    setupPrintStyles(receiptConfig) {
        // Remove existing print style if any
        const existingStyle = document.getElementById('pos-receipt-print-style');
        if (existingStyle) {
            existingStyle.remove();
        }

        // Create new print style
        const printStyle = document.createElement('style');
        printStyle.id = 'pos-receipt-print-style';
        printStyle.type = 'text/css';

        // Get configuration values
        const width = receiptConfig.width || 80.0;
        const marginTop = receiptConfig.margin_top || 0.0;
        const marginBottom = receiptConfig.margin_bottom || 0.0;
        const marginLeft = receiptConfig.margin_left || 0.0;
        const marginRight = receiptConfig.margin_right || 0.0;
        const fontSize = receiptConfig.font_size || 12.0;
        const fontSizeHeader = receiptConfig.font_size_header || 16.0;
        const fontSizeSmall = receiptConfig.font_size_small || 10.0;
        const lineHeight = receiptConfig.line_height || 1.3;

        const cssRules = `
            @media print {
                @page {
                    size: ${width}mm auto;
                    margin: ${marginTop}mm ${marginRight}mm ${marginBottom}mm ${marginLeft}mm;
                    padding: 0;
                }

                body {
                    margin: 0;
                    padding: 0;
                    width: ${width}mm;
                }

                .pos-receipt {
                    width: ${width}mm !important;
                    max-width: ${width}mm !important;
                    min-width: ${width}mm !important;
                    margin: 0 !important;
                    padding: ${marginTop}mm ${marginRight}mm ${marginBottom}mm ${marginLeft}mm !important;
                    font-size: ${fontSize}px !important;
                    line-height: ${lineHeight} !important;
                }

                .pos-receipt-company-name {
                    font-size: ${fontSizeHeader}px !important;
                    font-weight: bold !important;
                }

                .pos-receipt-contact {
                    font-size: ${fontSizeSmall}px !important;
                }

                .orderline, .paymentline {
                    font-size: ${fontSize}px !important;
                    line-height: ${lineHeight} !important;
                }
            }
        `;

        printStyle.innerHTML = cssRules;
        document.head.appendChild(printStyle);

        console.log(`Print styles set for ${width}mm receipt with custom settings`);
    },

    /**
     * Enhanced print receipt with custom configuration
     */
    async printReceipt() {
        // Get receipt configuration
        const receiptConfig = this.pos.getReceiptConfig();

        // Set up print styles based on configuration
        this.setupPrintStyles(receiptConfig);

        // Call original print method
        await super.printReceipt(...arguments);
    }
});
