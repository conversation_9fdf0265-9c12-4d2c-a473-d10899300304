/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

patch(ReceiptScreen.prototype, {
    /**
     * Enhanced print receipt with proper paper size handling
     */
    async printReceipt() {
        // Get receipt configuration
        const receiptConfig = this.pos.getReceiptConfig();
        const receiptSize = receiptConfig.size || '80mm';
        
        // Set up print styles based on receipt size
        this.setupPrintStyles(receiptSize);
        
        // Call original print method
        await super.printReceipt(...arguments);
    },

    /**
     * Setup print styles for specific receipt size
     */
    setupPrintStyles(receiptSize) {
        // Remove existing print style if any
        const existingStyle = document.getElementById('pos-receipt-print-style');
        if (existingStyle) {
            existingStyle.remove();
        }

        // Create new print style
        const printStyle = document.createElement('style');
        printStyle.id = 'pos-receipt-print-style';
        printStyle.type = 'text/css';

        let cssRules = '';

        if (receiptSize === '58mm') {
            cssRules = `
                @media print {
                    @page {
                        size: 58mm auto;
                        margin: 0;
                        padding: 0;
                    }
                    
                    body {
                        margin: 0;
                        padding: 0;
                        width: 58mm;
                    }
                    
                    .pos-receipt {
                        width: 58mm !important;
                        max-width: 58mm !important;
                        min-width: 58mm !important;
                        margin: 0 !important;
                        padding: 2mm !important;
                        font-size: 9px !important;
                        line-height: 1.1 !important;
                    }
                }
            `;
        } else if (receiptSize === '80mm') {
            cssRules = `
                @media print {
                    @page {
                        size: 80mm auto;
                        margin: 0;
                        padding: 0;
                    }
                    
                    body {
                        margin: 0;
                        padding: 0;
                        width: 80mm;
                    }
                    
                    .pos-receipt {
                        width: 80mm !important;
                        max-width: 80mm !important;
                        min-width: 80mm !important;
                        margin: 0 !important;
                        padding: 3mm !important;
                        font-size: 11px !important;
                        line-height: 1.3 !important;
                    }
                }
            `;
        }

        printStyle.innerHTML = cssRules;
        document.head.appendChild(printStyle);

        console.log(`Print styles set for ${receiptSize} receipt`);
    },

    /**
     * Enhanced print with browser print dialog customization
     */
    async doPrint() {
        const receiptConfig = this.pos.getReceiptConfig();
        const receiptSize = receiptConfig.size || '80mm';
        
        // Setup print styles
        this.setupPrintStyles(receiptSize);
        
        // Add receipt size class to body for print
        document.body.classList.add(`printing-${receiptSize.replace('mm', 'mm')}`);
        
        try {
            // Use browser print with custom settings
            if (window.print) {
                // Set print media type
                const mediaQueryList = window.matchMedia('print');
                
                // Trigger print
                window.print();
                
                console.log(`Printed ${receiptSize} receipt`);
            }
        } catch (error) {
            console.error('Print error:', error);
        } finally {
            // Clean up
            document.body.classList.remove(`printing-${receiptSize.replace('mm', 'mm')}`);
        }
    }
});

// Add print button enhancement
patch(ReceiptScreen.prototype, {
    /**
     * Override print button click to use enhanced print
     */
    onClickPrint() {
        this.doPrint();
    }
});
