/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";
import { PrintManager } from "./print_manager";

patch(PosStore.prototype, {
    /**
     * Initialize print manager
     */
    async setup() {
        await super.setup(...arguments);
        this.printManager = new PrintManager(this);
    },

    /**
     * Enhanced receipt header data with receipt configuration
     */
    getReceiptHeaderData(order) {
        const baseData = super.getReceiptHeaderData(...arguments);

        return {
            ...baseData,
            partner: order.partner_id,
            receipt_config: {
                size: this.config.receipt_size || '80mm',
                show_client: this.config.receipt_show_client !== false,
                show_cashier: this.config.receipt_show_cashier !== false,
                show_date_time: this.config.receipt_show_date_time !== false,
                custom_header: this.config.receipt_custom_header || '',
                custom_footer: this.config.receipt_custom_footer || '',
            }
        };
    },

    /**
     * Get receipt configuration for current POS
     */
    getReceiptConfig() {
        return {
            size: this.config.receipt_size || '80mm',
            show_client: this.config.receipt_show_client !== false,
            show_cashier: this.config.receipt_show_cashier !== false,
            show_date_time: this.config.receipt_show_date_time !== false,
            custom_header: this.config.receipt_custom_header || '',
            custom_footer: this.config.receipt_custom_footer || '',
        };
    },

    /**
     * Check if current receipt size is 58mm
     */
    isReceipt58mm() {
        return this.config.receipt_size === '58mm';
    },

    /**
     * Check if current receipt size is 80mm
     */
    isReceipt80mm() {
        return this.config.receipt_size === '80mm' || !this.config.receipt_size;
    },

    /**
     * Print receipt with proper size handling
     */
    async printReceiptWithSize(receiptElement) {
        const receiptConfig = this.getReceiptConfig();
        const receiptSize = receiptConfig.size;

        if (this.printManager) {
            await this.printManager.printReceipt(receiptElement, receiptSize);
        } else {
            // Fallback to default print
            window.print();
        }
    }
});
