/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";

patch(PosStore.prototype, {
    /**
     * Enhanced receipt header data with receipt configuration
     */
    getReceiptHeaderData(order) {
        const baseData = super.getReceiptHeaderData(...arguments);

        return {
            ...baseData,
            partner: order.partner_id,
            receipt_config: this.getReceiptConfig()
        };
    },

    /**
     * Get receipt configuration for current POS
     */
    getReceiptConfig() {
        return {
            size: this.config.receipt_size || '80mm',
            width: this.config.receipt_width || 80.0,
            margin_top: this.config.receipt_margin_top || 0.0,
            margin_bottom: this.config.receipt_margin_bottom || 0.0,
            margin_left: this.config.receipt_margin_left || 0.0,
            margin_right: this.config.receipt_margin_right || 0.0,
            font_size: this.config.receipt_font_size || 12.0,
            font_size_header: this.config.receipt_font_size_header || 16.0,
            font_size_small: this.config.receipt_font_size_small || 10.0,
            line_height: this.config.receipt_line_height || 1.3,
            show_client: this.config.receipt_show_client !== false,
            show_cashier: this.config.receipt_show_cashier !== false,
            show_date_time: this.config.receipt_show_date_time !== false,
            custom_header: this.config.receipt_custom_header || '',
            custom_footer: this.config.receipt_custom_footer || '',
        };
    },

    /**
     * Check if current receipt size is 58mm
     */
    isReceipt58mm() {
        return this.config.receipt_size === '58mm';
    },

    /**
     * Check if current receipt size is 80mm
     */
    isReceipt80mm() {
        return this.config.receipt_size === '80mm' || !this.config.receipt_size;
    }
});
