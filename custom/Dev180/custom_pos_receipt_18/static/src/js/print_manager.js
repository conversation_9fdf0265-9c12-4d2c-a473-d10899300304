/** @odoo-module */

/**
 * Print Manager for handling different receipt sizes
 * Manages print settings and paper size configuration
 */
export class PrintManager {
    constructor(pos) {
        this.pos = pos;
        this.currentReceiptSize = '80mm';
    }

    /**
     * Set up print environment for specific receipt size
     */
    setupPrintEnvironment(receiptSize) {
        this.currentReceiptSize = receiptSize;
        
        // Remove existing print styles
        this.removePrintStyles();
        
        // Add new print styles
        this.addPrintStyles(receiptSize);
        
        // Set page orientation and size
        this.setPageSettings(receiptSize);
    }

    /**
     * Remove existing print styles
     */
    removePrintStyles() {
        const existingStyles = document.querySelectorAll('.pos-receipt-print-style');
        existingStyles.forEach(style => style.remove());
    }

    /**
     * Add print styles for specific receipt size
     */
    addPrintStyles(receiptSize) {
        const style = document.createElement('style');
        style.className = 'pos-receipt-print-style';
        style.type = 'text/css';

        let css = '';
        
        if (receiptSize === '58mm') {
            css = this.get58mmPrintCSS();
        } else if (receiptSize === '80mm') {
            css = this.get80mmPrintCSS();
        }

        style.innerHTML = css;
        document.head.appendChild(style);
    }

    /**
     * Get CSS for 58mm receipt printing
     */
    get58mmPrintCSS() {
        return `
            @media print {
                @page {
                    size: 58mm auto;
                    margin: 0;
                    padding: 0;
                }
                
                html, body {
                    width: 58mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    font-size: 8px !important;
                }
                
                .pos-receipt {
                    width: 58mm !important;
                    max-width: 58mm !important;
                    min-width: 58mm !important;
                    margin: 0 !important;
                    padding: 1mm !important;
                    font-size: 8px !important;
                    line-height: 1.0 !important;
                }
                
                .pos-receipt-company-name {
                    font-size: 10px !important;
                    font-weight: bold !important;
                    text-align: center !important;
                    margin-bottom: 2mm !important;
                }
                
                .pos-receipt-contact {
                    font-size: 7px !important;
                    text-align: center !important;
                    margin-bottom: 2mm !important;
                }
                
                .orderline, .paymentline {
                    font-size: 8px !important;
                    margin-bottom: 1mm !important;
                    padding: 0.5mm 0 !important;
                }
                
                .pos-receipt-right-align {
                    float: right !important;
                }
                
                .pos-receipt-left-align {
                    float: left !important;
                }
            }
        `;
    }

    /**
     * Get CSS for 80mm receipt printing
     */
    get80mmPrintCSS() {
        return `
            @media print {
                @page {
                    size: 80mm auto;
                    margin: 0;
                    padding: 0;
                }
                
                html, body {
                    width: 80mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    font-size: 10px !important;
                }
                
                .pos-receipt {
                    width: 80mm !important;
                    max-width: 80mm !important;
                    min-width: 80mm !important;
                    margin: 0 !important;
                    padding: 2mm !important;
                    font-size: 10px !important;
                    line-height: 1.2 !important;
                }
                
                .pos-receipt-company-name {
                    font-size: 14px !important;
                    font-weight: bold !important;
                    text-align: center !important;
                    margin-bottom: 3mm !important;
                }
                
                .pos-receipt-contact {
                    font-size: 9px !important;
                    text-align: center !important;
                    margin-bottom: 3mm !important;
                }
                
                .orderline, .paymentline {
                    font-size: 10px !important;
                    margin-bottom: 2mm !important;
                    padding: 1mm 0 !important;
                }
                
                .pos-receipt-right-align {
                    float: right !important;
                }
                
                .pos-receipt-left-align {
                    float: left !important;
                }
            }
        `;
    }

    /**
     * Set page settings for printing
     */
    setPageSettings(receiptSize) {
        // This will be handled by CSS @page rules
        console.log(`Page settings configured for ${receiptSize} receipt`);
    }

    /**
     * Print receipt with proper size settings
     */
    async printReceipt(receiptElement, receiptSize) {
        // Setup print environment
        this.setupPrintEnvironment(receiptSize);
        
        // Add receipt size class to body
        document.body.classList.add(`printing-${receiptSize.replace('mm', 'mm')}`);
        
        try {
            // Focus on the receipt element
            if (receiptElement) {
                receiptElement.focus();
            }
            
            // Trigger browser print
            window.print();
            
            console.log(`Receipt printed in ${receiptSize} format`);
            
        } catch (error) {
            console.error('Print error:', error);
            throw error;
        } finally {
            // Clean up
            document.body.classList.remove(`printing-${receiptSize.replace('mm', 'mm')}`);
            
            // Remove print styles after a delay
            setTimeout(() => {
                this.removePrintStyles();
            }, 1000);
        }
    }

    /**
     * Get print instructions for user
     */
    getPrintInstructions(receiptSize) {
        const instructions = {
            '58mm': {
                paperSize: '58mm thermal paper',
                printerSettings: 'Set printer to 58mm paper width, auto margins',
                browserSettings: 'In print dialog: More settings > Paper size > Custom (58mm x auto)'
            },
            '80mm': {
                paperSize: '80mm thermal paper',
                printerSettings: 'Set printer to 80mm paper width, auto margins',
                browserSettings: 'In print dialog: More settings > Paper size > Custom (80mm x auto)'
            }
        };
        
        return instructions[receiptSize] || instructions['80mm'];
    }
}
