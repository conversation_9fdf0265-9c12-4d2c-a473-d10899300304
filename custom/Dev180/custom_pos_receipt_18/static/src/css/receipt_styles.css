/* Custom POS Receipt Styles for Multiple Sizes */

/* 58mm Receipt Styles - Screen Display */
.receipt-58mm {
    max-width: 58mm;
    font-size: 11px;
    line-height: 1.2;
    margin: 0 auto;
}

.receipt-58mm .pos-receipt-contact {
    font-size: 10px;
    text-align: center;
}

.receipt-58mm .pos-receipt-company-name {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 5px;
}

.receipt-58mm .orderline-58mm {
    margin-bottom: 8px;
    padding-bottom: 3px;
    border-bottom: 1px dotted #ddd;
}

.receipt-58mm .paymentline-58mm {
    padding: 1px 0;
    font-size: 10px;
}

.receipt-58mm .pos-receipt-custom-header,
.receipt-58mm .pos-receipt-custom-footer {
    font-size: 10px;
    text-align: center;
}

/* 80mm Receipt Styles - Screen Display */
.receipt-80mm {
    max-width: 80mm;
    font-size: 12px;
    line-height: 1.4;
    margin: 0 auto;
}

.receipt-80mm .pos-receipt-contact {
    font-size: 11px;
    text-align: center;
}

.receipt-80mm .pos-receipt-company-name {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px;
}

.receipt-80mm .orderline-80mm {
    margin-bottom: 10px;
    padding-bottom: 5px;
}

.receipt-80mm .paymentline-80mm {
    padding: 3px 0;
    font-size: 12px;
}

.receipt-80mm .pos-receipt-custom-header,
.receipt-80mm .pos-receipt-custom-footer {
    font-size: 11px;
    text-align: center;
}

/* Common Styles */
.pos-receipt-cashier,
.pos-receipt-client {
    text-align: center;
    margin: 3px 0;
    font-weight: bold;
}

.pos-receipt-datetime {
    font-size: 0.9em;
    color: #666;
}

.pos-receipt-custom-header {
    border-bottom: 1px solid #000;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.pos-receipt-custom-footer {
    border-top: 1px solid #000;
    padding-top: 5px;
    margin-top: 10px;
}

/* Print Styles - Actual Paper Size Control */
@media print {
    /* Reset all default print styles */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Hide everything except receipt */
    body * {
        visibility: hidden;
    }

    .pos-receipt, .pos-receipt * {
        visibility: visible;
    }

    /* 58mm Print Settings */
    .receipt-58mm {
        width: 58mm !important;
        max-width: 58mm !important;
        min-width: 58mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 9px !important;
        line-height: 1.1 !important;
    }

    /* 80mm Print Settings */
    .receipt-80mm {
        width: 80mm !important;
        max-width: 80mm !important;
        min-width: 80mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 11px !important;
        line-height: 1.3 !important;
    }

    /* Page setup for 58mm */
    @page receipt-58mm {
        size: 58mm auto;
        margin: 0;
        padding: 0;
    }

    /* Page setup for 80mm */
    @page receipt-80mm {
        size: 80mm auto;
        margin: 0;
        padding: 0;
    }

    /* Apply page settings based on receipt type */
    .receipt-58mm {
        page: receipt-58mm;
    }

    .receipt-80mm {
        page: receipt-80mm;
    }

    /* Remove page breaks within receipt */
    .pos-receipt {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Ensure proper text sizing for print */
    .receipt-58mm .pos-receipt-company-name {
        font-size: 12px !important;
        font-weight: bold !important;
    }

    .receipt-58mm .pos-receipt-contact {
        font-size: 8px !important;
    }

    .receipt-80mm .pos-receipt-company-name {
        font-size: 14px !important;
        font-weight: bold !important;
    }

    .receipt-80mm .pos-receipt-contact {
        font-size: 10px !important;
    }

    /* Remove borders and shadows for print */
    .pos-receipt * {
        box-shadow: none !important;
        border-radius: 0 !important;
    }
}

/* Enhanced styling for better readability */
.pos-receipt-left-align {
    text-align: left;
}

.pos-receipt-right-align {
    text-align: right;
    float: right;
}

.pos-receipt-center-align {
    text-align: center;
}

/* Product line enhancements */
.orderline-58mm .pos-receipt-left-align,
.orderline-80mm .pos-receipt-left-align {
    clear: both;
}

.orderline-58mm .pos-receipt-right-align,
.orderline-80mm .pos-receipt-right-align {
    clear: both;
}

/* Payment line enhancements */
.paymentline-58mm,
.paymentline-80mm {
    clear: both;
    overflow: hidden;
}

/* Customer note styling */
.pos-receipt-customer-note {
    color: #666;
    font-style: italic;
    margin-top: 2px;
}

/* Print-specific body classes */
body.printing-58mm {
    margin: 0 !important;
    padding: 0 !important;
    width: 58mm !important;
}

body.printing-80mm {
    margin: 0 !important;
    padding: 0 !important;
    width: 80mm !important;
}

/* Additional print optimizations */
@media print {
    /* Ensure no page breaks in critical areas */
    .pos-receipt-company-name,
    .pos-receipt-contact,
    .orderline,
    .paymentline {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Optimize spacing for print */
    .receipt-58mm .orderline,
    .receipt-58mm .paymentline {
        margin-bottom: 2px !important;
        padding: 1px 0 !important;
    }

    .receipt-80mm .orderline,
    .receipt-80mm .paymentline {
        margin-bottom: 3px !important;
        padding: 2px 0 !important;
    }

    /* Ensure text is black for print */
    .pos-receipt * {
        color: #000 !important;
    }

    /* Remove background colors for print */
    .pos-receipt * {
        background: transparent !important;
        background-color: transparent !important;
    }

    /* Optimize font weights for thermal printers */
    .receipt-58mm .pos-receipt-company-name,
    .receipt-80mm .pos-receipt-company-name {
        font-weight: 900 !important;
    }

    /* Ensure proper line spacing for thermal printers */
    .receipt-58mm {
        line-height: 1.0 !important;
    }

    .receipt-80mm {
        line-height: 1.2 !important;
    }
}
