/* Custom POS Receipt Styles for Multiple Sizes */

/* 58mm Receipt Styles */
.receipt-58mm {
    max-width: 58mm;
    font-size: 11px;
    line-height: 1.2;
}

.receipt-58mm .pos-receipt-contact {
    font-size: 10px;
    text-align: center;
}

.receipt-58mm .pos-receipt-company-name {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 5px;
}

.receipt-58mm .orderline-58mm {
    margin-bottom: 8px;
    padding-bottom: 3px;
    border-bottom: 1px dotted #ddd;
}

.receipt-58mm .paymentline-58mm {
    padding: 1px 0;
    font-size: 10px;
}

.receipt-58mm .pos-receipt-custom-header,
.receipt-58mm .pos-receipt-custom-footer {
    font-size: 10px;
    text-align: center;
}

/* 80mm Receipt Styles */
.receipt-80mm {
    max-width: 80mm;
    font-size: 12px;
    line-height: 1.4;
}

.receipt-80mm .pos-receipt-contact {
    font-size: 11px;
    text-align: center;
}

.receipt-80mm .pos-receipt-company-name {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px;
}

.receipt-80mm .orderline-80mm {
    margin-bottom: 10px;
    padding-bottom: 5px;
}

.receipt-80mm .paymentline-80mm {
    padding: 3px 0;
    font-size: 12px;
}

.receipt-80mm .pos-receipt-custom-header,
.receipt-80mm .pos-receipt-custom-footer {
    font-size: 11px;
    text-align: center;
}

/* Common Styles */
.pos-receipt-cashier,
.pos-receipt-client {
    text-align: center;
    margin: 3px 0;
    font-weight: bold;
}

.pos-receipt-datetime {
    font-size: 0.9em;
    color: #666;
}

.pos-receipt-custom-header {
    border-bottom: 1px solid #000;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.pos-receipt-custom-footer {
    border-top: 1px solid #000;
    padding-top: 5px;
    margin-top: 10px;
}

/* Responsive adjustments */
@media print {
    .receipt-58mm {
        width: 58mm;
    }
    
    .receipt-80mm {
        width: 80mm;
    }
}

/* Enhanced styling for better readability */
.pos-receipt-left-align {
    text-align: left;
}

.pos-receipt-right-align {
    text-align: right;
    float: right;
}

.pos-receipt-center-align {
    text-align: center;
}

/* Product line enhancements */
.orderline-58mm .pos-receipt-left-align,
.orderline-80mm .pos-receipt-left-align {
    clear: both;
}

.orderline-58mm .pos-receipt-right-align,
.orderline-80mm .pos-receipt-right-align {
    clear: both;
}

/* Payment line enhancements */
.paymentline-58mm,
.paymentline-80mm {
    clear: both;
    overflow: hidden;
}

/* Customer note styling */
.pos-receipt-customer-note {
    color: #666;
    font-style: italic;
    margin-top: 2px;
}
