<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    
    <!-- 58mm Receipt Specific Styling -->
    <t t-name="custom_pos_receipt_18.Receipt58mm" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="t-attf-class">pos-receipt #{props.data.receipt_config and props.data.receipt_config.size === '58mm' ? 'receipt-58mm' : ''}</attribute>
        </xpath>
    </t>

    <!-- 58mm Product Lines - Compact Format -->
    <t t-name="custom_pos_receipt_18.Orderlines58mm" t-inherit="point_of_sale.Orderlines" t-inherit-mode="extension">
        <xpath expr="//t[@t-foreach='props.data.orderlines']" position="replace">
            <t t-foreach="props.data.orderlines" t-as="line" t-key="line.id">
                <t t-if="props.data.receipt_config and props.data.receipt_config.size === '58mm'">
                    <!-- 58mm Compact Format -->
                    <div class="orderline-58mm">
                        <!-- Product Name (truncated if too long) -->
                        <div class="pos-receipt-left-align" style="font-weight: bold;">
                            <t t-esc="line.product_name.length > 20 ? line.product_name.substring(0, 20) + '...' : line.product_name"/>
                        </div>
                        
                        <!-- Quantity x Price = Total (on same line) -->
                        <div class="pos-receipt-right-align">
                            <t t-esc="line.quantity"/> x <t t-esc="props.formatCurrency(line.price)"/> = <t t-esc="props.formatCurrency(line.price_display)"/>
                        </div>
                        
                        <!-- Discount if any -->
                        <t t-if="line.discount !== 0">
                            <div class="pos-receipt-right-align" style="font-size: 0.9em; color: #666;">
                                Discount: <t t-esc="line.discount"/>%
                            </div>
                        </t>
                    </div>
                </t>
                <t t-else="">
                    <!-- Standard 80mm Format -->
                    <div class="orderline">
                        <t t-esc="line.product_name"/>
                        <span t-esc="props.formatCurrency(line.price_display)" class="pos-receipt-right-align"/>
                    </div>
                    <t t-if="line.discount !== 0">
                        <div class="pos-receipt-left-align">
                            Discount: <t t-esc="line.discount"/>%
                        </div>
                    </t>
                    <t t-if="line.customer_note">
                        <div class="pos-receipt-left-align pos-receipt-customer-note">
                            <t t-esc="line.customer_note"/>
                        </div>
                    </t>
                </t>
            </t>
        </xpath>
    </t>

    <!-- 58mm Payment Lines - Compact Format -->
    <t t-name="custom_pos_receipt_18.Paymentlines58mm" t-inherit="point_of_sale.PaymentLines" t-inherit-mode="extension">
        <xpath expr="//t[@t-foreach='props.data.paymentlines']" position="replace">
            <t t-foreach="props.data.paymentlines" t-as="line" t-key="line.id">
                <t t-if="props.data.receipt_config and props.data.receipt_config.size === '58mm'">
                    <!-- 58mm Compact Payment Format -->
                    <div class="paymentline-58mm">
                        <t t-esc="line.name"/>
                        <span t-esc="props.formatCurrency(line.amount)" class="pos-receipt-right-align"/>
                    </div>
                </t>
                <t t-else="">
                    <!-- Standard 80mm Payment Format -->
                    <div class="paymentline">
                        <t t-esc="line.name"/>
                        <span t-esc="props.formatCurrency(line.amount)" class="pos-receipt-right-align"/>
                    </div>
                </t>
            </t>
        </xpath>
    </t>

</templates>
