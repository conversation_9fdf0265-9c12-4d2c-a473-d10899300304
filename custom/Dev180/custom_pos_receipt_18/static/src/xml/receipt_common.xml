<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    
    <!-- Enhanced Receipt Header with Configuration Support -->
    <t t-name="custom_pos_receipt_18.ReceiptHeader" t-inherit="point_of_sale.ReceiptHeader" t-inherit-mode="extension">
        <!-- Custom Header Text -->
        <xpath expr="//div[@class='pos-receipt-contact']" position="before">
            <t t-if="props.data.receipt_config and props.data.receipt_config.custom_header">
                <div class="pos-receipt-custom-header" style="text-align: center; font-weight: bold; margin-bottom: 10px;">
                    <t t-esc="props.data.receipt_config.custom_header"/>
                </div>
            </t>
        </xpath>

        <!-- Enhanced Cashier Display -->
        <xpath expr="//div[@t-if='props.data.cashier']" position="replace">
            <t t-if="props.data.receipt_config and props.data.receipt_config.show_cashier and props.data.cashier">
                <div class="pos-receipt-cashier">
                    <t t-if="props.data.receipt_config.size === '58mm'">
                        Cashier: <t t-esc='props.data.cashier' />
                    </t>
                    <t t-else="">
                        Served by: <t t-esc='props.data.cashier' />
                    </t>
                </div>
            </t>
        </xpath>

        <!-- Enhanced Client Display -->
        <xpath expr="//div[@t-if='props.data.cashier']" position="after">
            <t t-if="props.data.receipt_config and props.data.receipt_config.show_client and props.data.partner">
                <div class="pos-receipt-client">
                    <t t-if="props.data.receipt_config.size === '58mm'">
                        Client: <t t-esc='props.data.partner.name' />
                    </t>
                    <t t-else="">
                        Customer: <t t-esc='props.data.partner.name' />
                    </t>
                </div>
            </t>
        </xpath>

        <!-- Enhanced Date/Time Display -->
        <xpath expr="//div[@class='pos-receipt-contact']" position="after">
            <t t-if="props.data.receipt_config and props.data.receipt_config.show_date_time">
                <div class="pos-receipt-datetime" style="text-align: center; margin: 5px 0;">
                    <t t-if="props.data.receipt_config.size === '58mm'">
                        <t t-esc="props.data.date.localeString"/>
                    </t>
                    <t t-else="">
                        Date: <t t-esc="props.data.date.toLocaleDateString"/> | Time: <t t-esc="props.data.date.toLocaleTimeString"/>
                    </t>
                </div>
            </t>
        </xpath>
    </t>

    <!-- Enhanced Receipt Footer -->
    <t t-name="custom_pos_receipt_18.ReceiptFooter" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[@class='pos-receipt-amount receipt-change']" position="after">
            <t t-if="props.data.receipt_config and props.data.receipt_config.custom_footer">
                <br/>
                <div class="pos-receipt-custom-footer" style="text-align: center; margin-top: 10px; font-style: italic;">
                    <t t-esc="props.data.receipt_config.custom_footer"/>
                </div>
            </t>
        </xpath>
    </t>

</templates>
