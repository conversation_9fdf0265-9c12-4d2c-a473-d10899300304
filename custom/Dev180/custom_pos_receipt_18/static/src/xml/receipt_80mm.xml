<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    
    <!-- 80mm Receipt Specific Styling -->
    <t t-name="custom_pos_receipt_18.Receipt80mm" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="t-attf-class">pos-receipt #{props.data.receipt_config and props.data.receipt_config.size === '80mm' ? 'receipt-80mm' : ''}</attribute>
        </xpath>
    </t>

    <!-- 80mm Enhanced Product Lines -->
    <t t-name="custom_pos_receipt_18.Orderlines80mm" t-inherit="point_of_sale.Orderlines" t-inherit-mode="extension">
        <xpath expr="//t[@t-foreach='props.data.orderlines']" position="replace">
            <t t-foreach="props.data.orderlines" t-as="line" t-key="line.id">
                <t t-if="props.data.receipt_config and props.data.receipt_config.size === '80mm'">
                    <!-- 80mm Enhanced Format -->
                    <div class="orderline-80mm">
                        <!-- Product Name -->
                        <div class="pos-receipt-left-align" style="font-weight: bold; margin-bottom: 2px;">
                            <t t-esc="line.product_name"/>
                        </div>
                        
                        <!-- Quantity, Unit Price, and Total on separate line -->
                        <div style="display: flex; justify-content: space-between; font-size: 0.9em;">
                            <span>Qty: <t t-esc="line.quantity"/></span>
                            <span>Unit: <t t-esc="props.formatCurrency(line.price)"/></span>
                            <span>Total: <t t-esc="props.formatCurrency(line.price_display)"/></span>
                        </div>
                        
                        <!-- Discount if any -->
                        <t t-if="line.discount !== 0">
                            <div class="pos-receipt-left-align" style="font-size: 0.85em; color: #666; margin-top: 2px;">
                                Discount Applied: <t t-esc="line.discount"/>%
                            </div>
                        </t>
                        
                        <!-- Customer Note if any -->
                        <t t-if="line.customer_note">
                            <div class="pos-receipt-left-align pos-receipt-customer-note" style="font-size: 0.85em; font-style: italic; margin-top: 2px;">
                                Note: <t t-esc="line.customer_note"/>
                            </div>
                        </t>
                        
                        <!-- Separator line -->
                        <div style="border-bottom: 1px dotted #ccc; margin: 5px 0;"></div>
                    </div>
                </t>
                <t t-else="">
                    <!-- Fallback to standard format -->
                    <div class="orderline">
                        <t t-esc="line.product_name"/>
                        <span t-esc="props.formatCurrency(line.price_display)" class="pos-receipt-right-align"/>
                    </div>
                    <t t-if="line.discount !== 0">
                        <div class="pos-receipt-left-align">
                            Discount: <t t-esc="line.discount"/>%
                        </div>
                    </t>
                    <t t-if="line.customer_note">
                        <div class="pos-receipt-left-align pos-receipt-customer-note">
                            <t t-esc="line.customer_note"/>
                        </div>
                    </t>
                </t>
            </t>
        </xpath>
    </t>

    <!-- 80mm Enhanced Payment Lines -->
    <t t-name="custom_pos_receipt_18.Paymentlines80mm" t-inherit="point_of_sale.PaymentLines" t-inherit-mode="extension">
        <xpath expr="//t[@t-foreach='props.data.paymentlines']" position="replace">
            <t t-foreach="props.data.paymentlines" t-as="line" t-key="line.id">
                <t t-if="props.data.receipt_config and props.data.receipt_config.size === '80mm'">
                    <!-- 80mm Enhanced Payment Format -->
                    <div class="paymentline-80mm" style="display: flex; justify-content: space-between; padding: 2px 0;">
                        <span style="font-weight: bold;"><t t-esc="line.name"/></span>
                        <span style="font-weight: bold;"><t t-esc="props.formatCurrency(line.amount)"/></span>
                    </div>
                </t>
                <t t-else="">
                    <!-- Standard Payment Format -->
                    <div class="paymentline">
                        <t t-esc="line.name"/>
                        <span t-esc="props.formatCurrency(line.amount)" class="pos-receipt-right-align"/>
                    </div>
                </t>
            </t>
        </xpath>
    </t>

</templates>
