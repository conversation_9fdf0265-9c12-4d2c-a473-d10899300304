<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- POS Configuration Form View -->
        <record id="view_pos_config_form_receipt_settings" model="ir.ui.view">
            <field name="name">pos.config.form.receipt.settings</field>
            <field name="model">pos.config</field>
            <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
            <field name="arch" type="xml">
                <field name="name" position="after">
                    <group string="Receipt Settings" name="receipt_settings">
                        <group string="Receipt Size &amp; Format">
                            <field name="receipt_size" widget="radio"/>
                            <field name="receipt_width" attrs="{'invisible': [('receipt_size', '!=', 'custom')]}"/>
                        </group>
                        <group string="Margins (mm)">
                            <field name="receipt_margin_top"/>
                            <field name="receipt_margin_bottom"/>
                            <field name="receipt_margin_left"/>
                            <field name="receipt_margin_right"/>
                        </group>
                        <group string="Font Settings">
                            <field name="receipt_font_size"/>
                            <field name="receipt_font_size_header"/>
                            <field name="receipt_font_size_small"/>
                            <field name="receipt_line_height"/>
                        </group>
                        <group string="Display Options">
                            <field name="receipt_show_client"/>
                            <field name="receipt_show_cashier"/>
                            <field name="receipt_show_date_time"/>
                        </group>
                        <group string="Custom Content">
                            <field name="receipt_custom_header" placeholder="Enter custom header text (optional)"/>
                            <field name="receipt_custom_footer" placeholder="Enter custom footer text (optional)"/>
                        </group>
                    </group>
                </field>
            </field>
        </record>
    </data>
</odoo>
