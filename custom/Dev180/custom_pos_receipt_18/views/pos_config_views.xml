<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- POS Configuration Form View -->
        <record id="view_pos_config_form_receipt_settings" model="ir.ui.view">
            <field name="name">pos.config.form.receipt.settings</field>
            <field name="model">pos.config</field>
            <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='iface']" position="after">
                    <page string="Receipt Settings" name="receipt_settings">
                        <group>
                            <group string="Receipt Size &amp; Format">
                                <field name="receipt_size" widget="radio"/>
                                <field name="receipt_show_client"/>
                                <field name="receipt_show_cashier"/>
                                <field name="receipt_show_date_time"/>
                            </group>
                            <group string="Custom Content">
                                <field name="receipt_custom_header" placeholder="Enter custom header text (optional)"/>
                                <field name="receipt_custom_footer" placeholder="Enter custom footer text (optional)"/>
                            </group>
                        </group>
                        <div class="alert alert-info" role="alert">
                            <strong>Receipt Size Guide:</strong>
                            <ul>
                                <li><strong>58mm:</strong> Compact receipt suitable for small thermal printers. Ideal for mobile POS or limited space.</li>
                                <li><strong>80mm:</strong> Standard receipt size for regular thermal printers. Provides more space for information.</li>
                            </ul>
                        </div>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
