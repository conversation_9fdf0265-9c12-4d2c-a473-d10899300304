# POS Custom Receipt 18 - Multi Size

Enhanced Point of Sale Receipt Module for Odoo 18 with support for multiple receipt sizes.

## Features

### 🎯 **Multi-Size Receipt Support**
- **58mm Receipt**: Compact format for small thermal printers
- **80mm Receipt**: Standard format for regular thermal printers
- Configurable per POS configuration

### 🛠️ **Configurable Options**
- Receipt size selection (58mm/80mm)
- Show/hide client name on receipt
- Show/hide cashier name on receipt
- Show/hide date & time on receipt
- Custom header text
- Custom footer text

### 📱 **58mm Receipt Features**
- Compact layout optimized for small printers
- Truncated product names for space efficiency
- Condensed quantity and pricing display
- Minimal spacing for paper conservation

### 🖨️ **80mm Receipt Features**
- Enhanced layout with detailed information
- Full product names and descriptions
- Detailed quantity, unit price, and total display
- Customer notes support
- Professional formatting with separators

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list in Odoo
3. Install the "POS Custom Receipt 18 - Multi Size" module

## Configuration

### Setting up Receipt Size

1. Go to **Point of Sale > Configuration > Point of Sale**
2. Select your POS configuration
3. Navigate to the **Receipt Settings** tab
4. Choose your preferred receipt size:
   - **58mm - Compact Receipt**: For small thermal printers
   - **80mm - Standard Receipt**: For regular thermal printers

### Additional Settings

- **Show Client Name**: Display customer name when selected
- **Show Cashier Name**: Display cashier information
- **Show Date & Time**: Display transaction date and time
- **Custom Header**: Add custom text at the top of receipts
- **Custom Footer**: Add custom text at the bottom of receipts

## Technical Details

### File Structure
```
custom_pos_receipt_18/
├── __init__.py
├── __manifest__.py
├── README.md
├── models/
│   ├── __init__.py
│   └── pos_config.py
├── views/
│   └── pos_config_views.xml
└── static/
    ├── description/
    ├── src/
    │   ├── css/
    │   │   └── receipt_styles.css
    │   ├── js/
    │   │   └── models.js
    │   └── xml/
    │       ├── receipt_common.xml
    │       ├── receipt_58mm.xml
    │       └── receipt_80mm.xml
```

### Key Components

- **models/pos_config.py**: Extends POS configuration with receipt settings
- **views/pos_config_views.xml**: Admin interface for receipt configuration
- **static/src/js/models.js**: Frontend logic for receipt handling
- **static/src/xml/**: Receipt templates for different sizes
- **static/src/css/**: Styling for different receipt formats

## Usage

1. Configure your POS with the desired receipt size
2. Set up any custom header/footer text
3. Enable/disable optional fields as needed
4. Start using your POS - receipts will automatically use the configured format

## Compatibility

- **Odoo Version**: 18.0
- **Dependencies**: base, point_of_sale
- **Browser Support**: All modern browsers
- **Printer Support**: Thermal printers (58mm and 80mm)

## Support

For support and customization requests, please contact the module author.

## License

OPL-1 (Odoo Proprietary License)

## Author

Kanak Infosystems LLP.
Website: https://www.kanakinfosystems.com
