# -*- coding: utf-8 -*-

def migrate(cr, version):
    """Add missing columns to pos_payment table"""
    
    # Check if columns exist and add them if they don't
    columns_to_add = [
        ('multi_currency_total', 'NUMERIC'),
        ('rate_of_currency', 'DOUBLE PRECISION'),
        ('selected_currency', 'VARCHAR'),
        ('symbol_of_currency', 'VARCHAR'),
        ('foreign_currency_id', 'INTEGER'),
    ]
    
    for column_name, column_type in columns_to_add:
        # Check if column exists
        cr.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='pos_payment' AND column_name=%s
        """, (column_name,))
        
        if not cr.fetchone():
            # Add the column
            if column_type == 'NUMERIC':
                cr.execute(f"ALTER TABLE pos_payment ADD COLUMN {column_name} NUMERIC DEFAULT 0.0")
            elif column_type == 'DOUBLE PRECISION':
                cr.execute(f"ALTER TABLE pos_payment ADD COLUMN {column_name} DOUBLE PRECISION DEFAULT 0.0")
            elif column_type == 'VARCHAR':
                cr.execute(f"ALTER TABLE pos_payment ADD COLUMN {column_name} VARCHAR")
            elif column_type == 'INTEGER':
                cr.execute(f"ALTER TABLE pos_payment ADD COLUMN {column_name} INTEGER")
            
            print(f"Added column {column_name} to pos_payment table")
        else:
            print(f"Column {column_name} already exists in pos_payment table")
    
    # Add foreign key constraint for foreign_currency_id if it doesn't exist
    cr.execute("""
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name='pos_payment' AND constraint_name='pos_payment_foreign_currency_id_fkey'
    """)
    
    if not cr.fetchone():
        try:
            cr.execute("""
                ALTER TABLE pos_payment 
                ADD CONSTRAINT pos_payment_foreign_currency_id_fkey 
                FOREIGN KEY (foreign_currency_id) REFERENCES res_currency(id) ON DELETE SET NULL
            """)
            print("Added foreign key constraint for foreign_currency_id")
        except Exception as e:
            print(f"Could not add foreign key constraint: {e}")
