# -*- coding: utf-8 -*-
{
    'name': 'POS Multi-Currency Payment',
    'version': '1.0',
    'category': 'Sales/Point of Sale',
    'summary': 'Allow payment methods with different currencies in POS',
    'description': """
POS Multi-Currency Payment
==========================
This module allows creating payment methods with different currencies than the company currency in the Point of Sale.
Features:
- Configure payment methods with specific currencies
- Accept payments in multiple currencies in a single POS order
- View reports of sales by currency
- Session reports show payment totals by currency
    """,
    'depends': ['point_of_sale'],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_payment_method_views.xml',
        'views/pos_config_views.xml',
        'views/pos_session_views.xml',
        'views/pos_order_views.xml',
        'views/pos_session_sales_by_currency_views.xml',
        'report/pos_order_report_views.xml',
    ],
    'demo': [
        'demo/demo.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'pos_multi_currency_payment/static/src/app/models/pos_payment_method.js',
            'pos_multi_currency_payment/static/src/app/models/pos_payment.js',
            'pos_multi_currency_payment/static/src/app/models/pos_orderline.js',
            'pos_multi_currency_payment/static/src/app/models/pos_order.js',
            'pos_multi_currency_payment/static/src/app/models/pos_receipt.js',
            'pos_multi_currency_payment/static/src/app/screens/payment_screen/payment_screen.js',
            'pos_multi_currency_payment/static/src/app/screens/receipt_screen/receipt_screen_patch.js',
            'pos_multi_currency_payment/static/src/app/components/**/*',
            'pos_multi_currency_payment/static/src/css/payment_screen.scss',
            'pos_multi_currency_payment/static/src/css/receipt.scss',
        ],
        'web.assets_qweb': [
            'pos_multi_currency_payment/static/src/xml/**/*',
        ],
    },
    'installable': True,
    'application': False,
    'license': 'LGPL-3',
    'images': ['static/description/banner.png'],
    'author': 'Odoo Developer',
    'website': 'https://www.odoo.com',
    'maintainer': 'Odoo Developer',
    'auto_install': False,
}
