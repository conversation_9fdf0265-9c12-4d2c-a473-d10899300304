# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class PosConfig(models.Model):
    _inherit = 'pos.config'

    allow_multi_currency_payment = fields.Boolean(
        string='Allow Multi-Currency Payment',
        default=False,
        help='If enabled, payment methods with different currencies can be used in this POS.'
    )

    @api.constrains('pricelist_id', 'use_pricelist', 'available_pricelist_ids', 'journal_id', 'invoice_journal_id', 'payment_method_ids')
    def _check_currencies(self):
        """Override currency check to allow payment methods with different currencies if enabled"""
        for config in self:
            if config.use_pricelist and config.pricelist_id and config.pricelist_id not in config.available_pricelist_ids:
                raise ValidationError(_("The default pricelist must be included in the available pricelists."))

            # Skip payment method currency check if multi-currency is enabled
            if not config.allow_multi_currency_payment:
                # Check if the config's payment methods are compatible with its currency
                for pm in config.payment_method_ids:
                    if pm.journal_id and pm.journal_id.currency_id and pm.journal_id.currency_id != config.currency_id:
                        raise ValidationError(_("All payment methods must be in the same currency as the Sales Journal or the company currency if that is not set."))

            if config.use_pricelist and any(config.available_pricelist_ids.mapped(lambda pricelist: pricelist.currency_id != config.currency_id)):
                raise ValidationError(_("All available pricelists must be in the same currency as the company or"
                                      " as the Sales Journal set on this point of sale if you use"
                                      " the Accounting application."))
            if config.invoice_journal_id.currency_id and config.invoice_journal_id.currency_id != config.currency_id:
                raise ValidationError(_("The invoice journal must be in the same currency as the Sales Journal or the company currency if that is not set."))

    def _get_currency_rates(self):
        """Get currency exchange rates for POS"""
        rates = {}
        if self.allow_multi_currency_payment:
            # Get all currencies used by payment methods
            currencies = self.payment_method_ids.filtered('use_specific_currency').mapped('currency_id')
            for currency in currencies:
                if currency != self.currency_id:
                    rate = self.env['res.currency']._get_conversion_rate(
                        self.currency_id, currency, self.company_id, fields.Date.today())
                    rates[currency.name] = rate
        return rates
