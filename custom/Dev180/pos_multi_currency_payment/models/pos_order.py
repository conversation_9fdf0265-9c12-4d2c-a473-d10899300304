# -*- coding: utf-8 -*-
from odoo import api, fields, models, _


class PosOrder(models.Model):
    _inherit = 'pos.order'

    has_multi_currency_payments = fields.Boolean(
        string='Has Multi-Currency Payments',
        compute='_compute_has_multi_currency_payments',
        store=True,
        help='True if this order has payments in multiple currencies'
    )

    # Fields for multi-currency support
    payment_currency_id = fields.Many2one(
        'res.currency',
        string='Payment Currency',
        help='The currency used for payment'
    )
    payment_currency_rate = fields.Float(
        string='Payment Currency Rate',
        digits=(12, 6),
        help='The exchange rate between the payment currency and the order currency'
    )
    amount_in_payment_currency = fields.Monetary(
        string='Amount in Payment Currency',
        currency_field='payment_currency_id',
        help='The total amount in payment currency'
    )
    amount_paid_in_payment_currency = fields.Monetary(
        string='Paid Amount in Payment Currency',
        currency_field='payment_currency_id',
        help='The paid amount in payment currency'
    )

    @api.depends('payment_ids.payment_currency_id')
    def _compute_has_multi_currency_payments(self):
        """Check if the order has payments in multiple currencies"""
        for order in self:
            currencies = order.payment_ids.mapped('payment_currency_id')
            order.has_multi_currency_payments = len(currencies) > 1 or (
                currencies and currencies[0] != order.currency_id
            )

    def _export_for_ui(self, order):
        """Override to add multi-currency payment information"""
        result = super()._export_for_ui(order)

        # Add payment currency information
        if result.get('payment_ids'):
            for i, payment in enumerate(order.payment_ids):
                if payment.payment_currency_id:
                    result['payment_ids'][i].update({
                        'payment_currency_id': {
                            'id': payment.payment_currency_id.id,
                            'name': payment.payment_currency_id.name,
                            'symbol': payment.payment_currency_id.symbol,
                            'position': payment.payment_currency_id.position,
                            'decimal_places': payment.payment_currency_id.decimal_places,
                        },
                        'payment_currency_rate': payment.payment_currency_rate,
                        'amount_in_payment_currency': payment.amount_in_payment_currency,
                    })

        # Add order currency information
        if order.payment_currency_id:
            result.update({
                'payment_currency_id': {
                    'id': order.payment_currency_id.id,
                    'name': order.payment_currency_id.name,
                    'symbol': order.payment_currency_id.symbol,
                    'position': order.payment_currency_id.position,
                    'decimal_places': order.payment_currency_id.decimal_places,
                },
                'payment_currency_rate': order.payment_currency_rate,
                'amount_in_payment_currency': order.amount_in_payment_currency,
                'amount_paid_in_payment_currency': order.amount_paid_in_payment_currency,
            })

        return result

    @api.model
    def _process_order(self, order, existing_order):
        """Override to handle multi-currency payments"""
        # Process the order normally
        result = super()._process_order(order, existing_order)

        # If the order was processed successfully
        if result:
            # Get the created/updated order
            pos_order = self.browse(result)

            # Check if there are payments with different currencies
            payment_currencies = pos_order.payment_ids.mapped('payment_currency_id')
            if payment_currencies:
                # Use the first payment currency as the order payment currency
                payment_currency = payment_currencies[0]

                # Calculate the exchange rate
                order_currency = pos_order.currency_id
                if order_currency.id != payment_currency.id:
                    rate = self.env['res.currency']._get_conversion_rate(
                        payment_currency, order_currency, pos_order.company_id, pos_order.date_order.date())
                else:
                    rate = 1.0

                # Update the order with payment currency information
                pos_order.write({
                    'payment_currency_id': payment_currency.id,
                    'payment_currency_rate': rate,
                    'amount_in_payment_currency': pos_order.amount_total / rate if rate else 0,
                    'amount_paid_in_payment_currency': pos_order.amount_paid / rate if rate else 0,
                })

                # Update order lines with payment currency information
                for line in pos_order.lines:
                    line.write({
                        'payment_currency_id': payment_currency.id,
                        'payment_currency_rate': rate,
                        'price_subtotal_in_payment_currency': line.price_subtotal / rate if rate else 0,
                        'price_subtotal_incl_in_payment_currency': line.price_subtotal_incl / rate if rate else 0,
                    })

        return result
