# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.tools import float_is_zero
from odoo.tools.misc import formatLang


class PosSession(models.Model):
    _inherit = 'pos.session'

    payment_summary_by_currency = fields.Text(
        string='Payment Summary by Currency',
        compute='_compute_payment_summary_by_currency',
        help='Summary of payments by currency'
    )

    sales_by_currency_ids = fields.One2many(
        'pos.session.sales.by.currency',
        'session_id',
        string='Sales by Currency',
        compute='_compute_sales_by_currency',
        help='Sales totals grouped by currency'
    )

    @api.depends('order_ids.payment_ids', 'order_ids.payment_ids.amount', 'order_ids.payment_ids.payment_currency_id')
    def _compute_payment_summary_by_currency(self):
        """Compute a summary of payments grouped by currency"""
        for session in self:
            payments = self.env['pos.payment'].search([('pos_order_id.session_id', '=', session.id)])
            if not payments:
                session.payment_summary_by_currency = _("No payments")
                continue

            # Group payments by currency
            currency_totals = {}
            for payment in payments:
                currency = payment.payment_currency_id or payment.currency_id
                if currency not in currency_totals:
                    currency_totals[currency] = 0.0

                # Use amount_in_payment_currency if available, otherwise use amount
                amount = payment.amount_in_payment_currency if payment.payment_currency_id else payment.amount
                currency_totals[currency] += amount

            # Format the summary
            summary_lines = []
            for currency, total in currency_totals.items():
                if not float_is_zero(total, precision_rounding=currency.rounding):
                    formatted_amount = formatLang(self.env, total, currency_obj=currency)
                    summary_lines.append(f"{currency.name}: {formatted_amount}")

            session.payment_summary_by_currency = "\n".join(summary_lines) if summary_lines else _("No payments")

    def _update_amounts(self, old_amounts, amounts_to_add, date, payment_currency=None, payment_rate=None):
        """Override to handle multi-currency payments"""
        # Handle standard case
        if not payment_currency or not payment_rate or payment_currency == self.currency_id:
            return super()._update_amounts(old_amounts, amounts_to_add, date)

        # Handle multi-currency case
        new_amounts = {}

        # Convert amount from payment currency to session currency
        converted_amount = amounts_to_add['amount'] * payment_rate

        # Update amounts
        new_amounts['amount'] = old_amounts['amount'] + converted_amount

        # Convert to company currency if needed
        if self.currency_id != self.company_id.currency_id:
            amount_in_company_currency = self.currency_id._convert(
                converted_amount,
                self.company_id.currency_id,
                self.company_id,
                date
            )
            new_amounts['amount_converted'] = old_amounts['amount_converted'] + amount_in_company_currency
        else:
            new_amounts['amount_converted'] = new_amounts['amount']

        return new_amounts

    @api.depends('order_ids', 'order_ids.payment_currency_id', 'order_ids.amount_in_payment_currency')
    def _compute_sales_by_currency(self):
        """Compute sales totals grouped by currency"""
        for session in self:
            # Delete existing records
            self.env['pos.session.sales.by.currency'].search([('session_id', '=', session.id)]).unlink()

            # Group orders by payment currency
            currency_totals = {}
            for order in session.order_ids:
                if order.payment_currency_id:
                    currency = order.payment_currency_id
                    if currency not in currency_totals:
                        currency_totals[currency] = {
                            'currency': currency,
                            'total': 0.0,
                            'count': 0
                        }

                    currency_totals[currency]['total'] += order.amount_in_payment_currency
                    currency_totals[currency]['count'] += 1

            # Create records for each currency
            sales_by_currency = self.env['pos.session.sales.by.currency']
            for data in currency_totals.values():
                if not float_is_zero(data['total'], precision_rounding=data['currency'].rounding):
                    sales_by_currency |= self.env['pos.session.sales.by.currency'].create({
                        'session_id': session.id,
                        'currency_id': data['currency'].id,
                        'amount': data['total'],
                        'order_count': data['count']
                    })

            session.sales_by_currency_ids = sales_by_currency

    def _get_payment_summary_by_currency_values(self):
        """Get payment summary values for reporting"""
        self.ensure_one()

        # Get all payments for this session
        payments = self.env['pos.payment'].search([('pos_order_id.session_id', '=', self.id)])

        # Group payments by currency and payment method
        currency_method_totals = {}
        for payment in payments:
            currency = payment.payment_currency_id or payment.currency_id
            method = payment.payment_method_id

            key = (currency.id, method.id)
            if key not in currency_method_totals:
                currency_method_totals[key] = {
                    'currency': currency,
                    'method': method,
                    'total': 0.0
                }

            # Use amount_in_payment_currency if available, otherwise use amount
            amount = payment.amount_in_payment_currency if payment.payment_currency_id else payment.amount
            currency_method_totals[key]['total'] += amount

        # Format the results
        result = []
        for data in currency_method_totals.values():
            if not float_is_zero(data['total'], precision_rounding=data['currency'].rounding):
                result.append({
                    'currency_name': data['currency'].name,
                    'currency_symbol': data['currency'].symbol,
                    'method_name': data['method'].name,
                    'total': data['total'],
                    'formatted_total': formatLang(self.env, data['total'], currency_obj=data['currency'])
                })

        return result

    def _loader_params_res_currency(self):
        """Load currency rates for POS"""
        result = super()._loader_params_res_currency()

        # Add currency rates to the loaded data
        if self.config_id.allow_multi_currency_payment:
            result['currency_rates'] = self.config_id._get_currency_rates()

        return result
