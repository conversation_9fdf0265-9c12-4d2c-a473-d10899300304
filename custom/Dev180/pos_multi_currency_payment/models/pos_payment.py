# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    # Add fields for payment currency (following os_pos_multi_currency pattern)
    payment_currency_id = fields.Many2one('res.currency', string='Payment Currency',
                                        help='The currency in which the payment was made')
    payment_currency_rate = fields.Float(string='Payment Currency Rate', digits=(12, 6),
                                       help='The exchange rate between the payment currency and the order currency')
    amount_in_payment_currency = fields.Monetary(string='Amount in Payment Currency',
                                               currency_field='payment_currency_id',
                                               help='The payment amount in the payment currency')

    # Fields following os_pos_multi_currency pattern
    rate_of_currency = fields.Float(string="Conversion Rate", help="Conversion rate of currency.")
    symbol_of_currency = fields.Char(string="Currency", help="Currency Symbol.")
    foreign_currency_id = fields.Many2one('res.currency', compute='_compute_foreign_currency', store=True)
    multi_currency_total = fields.Monetary(string="Amount Currency", help="Amount Total in Selected Currency.",
                                           currency_field="foreign_currency_id")
    selected_currency = fields.Char(string="Selected Currency", help="Selected currency name")

    @api.depends('symbol_of_currency')
    def _compute_foreign_currency(self):
        for rec in self:
            rec.foreign_currency_id = False
            if rec.symbol_of_currency:
                currency = self.env['res.currency'].search([('symbol', 'ilike', rec.symbol_of_currency)], limit=1)
                if currency:
                    rec.foreign_currency_id = currency.id

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to set payment currency and rate"""
        for vals in vals_list:
            if 'payment_method_id' in vals:
                payment_method = self.env['pos.payment.method'].browse(vals['payment_method_id'])
                if payment_method.use_specific_currency and payment_method.currency_id:
                    # Set payment currency
                    vals['payment_currency_id'] = payment_method.currency_id.id

                    # Get order currency
                    if 'pos_order_id' in vals:
                        order = self.env['pos.order'].browse(vals['pos_order_id'])
                        order_currency = order.currency_id

                        # Get exchange rate from order currency to payment currency
                        if order_currency.id != payment_method.currency_id.id:
                            vals['payment_currency_rate'] = self.env['res.currency']._get_conversion_rate(
                                order_currency, payment_method.currency_id, order.company_id, order.date_order.date())
                        else:
                            vals['payment_currency_rate'] = 1.0

                        # Set amount in payment currency (convert from order currency to payment currency)
                        if 'amount' in vals and vals['payment_currency_rate']:
                            vals['amount_in_payment_currency'] = vals['amount'] * vals['payment_currency_rate']

        return super(PosPayment, self).create(vals_list)

    def _create_payment_moves(self, is_reverse=False):
        """Override to handle multi-currency payments"""
        result = self.env['account.move']
        credit_line_ids = []
        change_payment = self.filtered(lambda p: p.is_change and p.payment_method_id.type == 'cash')
        payment_to_change = self.filtered(lambda p: not p.is_change and p.payment_method_id.type == 'cash')[:1]

        for payment in self - change_payment:
            order = payment.pos_order_id
            payment_method = payment.payment_method_id

            # Skip if pay_later or zero amount
            if payment_method.type == 'pay_later' or self.env['account.move.line']._is_zero_amount(order.currency_id, payment.amount):
                continue

            accounting_partner = self.env["res.partner"]._find_accounting_partner(payment.partner_id)
            pos_session = order.session_id
            journal = pos_session.config_id.journal_id

            # Handle change payments
            if change_payment and payment == payment_to_change:
                payment_amount = payment.amount - sum(change_payment.mapped('amount'))
            else:
                payment_amount = payment.amount

            # Skip if zero amount after change
            if self.env['account.move.line']._is_zero_amount(order.currency_id, payment_amount):
                continue

            # Create payment move
            payment_move = self.env['account.move'].create({
                'journal_id': payment_method.journal_id.id if payment_method.journal_id else journal.id,
                'date': payment.payment_date.date(),
                'ref': pos_session.name,
                'partner_id': accounting_partner.id,
            })

            result |= payment_move
            payment.write({'account_move_id': payment_move.id})

            # Handle multi-currency payments
            if payment.payment_currency_id and payment.payment_currency_id != order.currency_id:
                # Use payment currency and rate for the move
                amounts = pos_session._update_amounts(
                    {'amount': 0, 'amount_converted': 0},
                    {'amount': payment_amount},
                    payment.payment_date,
                    payment_currency=payment.payment_currency_id,
                    payment_rate=payment.payment_currency_rate
                )
            else:
                # Use standard currency handling
                amounts = pos_session._update_amounts(
                    {'amount': 0, 'amount_converted': 0},
                    {'amount': payment_amount},
                    payment.payment_date
                )

            # Create credit line
            credit_line_vals = pos_session._credit_amounts({
                'account_id': accounting_partner.with_company(order.company_id).property_account_receivable_id.id,
                'partner_id': accounting_partner.id,
                'move_id': payment_move.id,
            }, amounts['amount'], amounts['amount_converted'])

            # Handle split transactions
            is_split_transaction = payment.payment_method_id.split_transactions
            if is_split_transaction and is_reverse:
                credit_line_ids.append(credit_line_vals['id'])

        # Process credit lines for split transactions
        if credit_line_ids:
            self.env['account.move.line'].browse(credit_line_ids).reconcile()

        return result

    def _export_for_ui(self, payment):
        """Export payment data for UI (following os_pos_multi_currency pattern)"""
        result = super(PosPayment, self)._export_for_ui(payment)
        result["rate_of_currency"] = payment.rate_of_currency
        result["symbol_of_currency"] = payment.symbol_of_currency
        result["multi_currency_total"] = payment.multi_currency_total
        result["selected_currency"] = payment.selected_currency
        return result
