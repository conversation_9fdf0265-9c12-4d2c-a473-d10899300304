/* Receipt styling for multi-currency payments */

.pos-receipt {
    .paymentlines {
        margin-bottom: 4px;

        .payment-line-local {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .payment-amount-foreign-box {
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 8px;
            margin: 4px 0;
            background-color: #f8f9fa;

            .foreign-currency-content {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .foreign-currency-label {
                    font-weight: bold;
                    color: #4CAF50;
                    font-size: 0.9em;
                }

                .foreign-currency-amount {
                    font-weight: bold;
                    color: #2E7D32;
                    font-size: 1.1em;
                }
            }
        }
    }
}
