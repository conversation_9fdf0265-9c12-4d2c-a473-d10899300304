import { patch } from "@web/core/utils/patch";
import { Order } from "@point_of_sale/app/store/models";

// Patch the Order model to ensure payment data is available for receipts
patch(Order.prototype, {
    /**
     * Override the export_for_printing method to include multi-currency payment info
     */
    export_for_printing() {
        const result = super.export_for_printing();

        // Add multi-currency payment information to payment lines
        if (result.paymentlines && this.payment_ids) {
            result.paymentlines = result.paymentlines.map(line => {
                // Find the corresponding payment line in the order
                const paymentLine = this.payment_ids.find(p =>
                    p.payment_method_id && p.payment_method_id.name === line.name &&
                    Math.abs(p.amount - line.amount) < 0.01
                );

                if (paymentLine) {
                    // Check if payment method has a specific currency
                    const paymentMethod = paymentLine.payment_method_id;
                    if (paymentMethod.use_specific_currency && paymentMethod.currency_id) {
                        // Calculate the foreign currency amount
                        const rate = this.pos.currency_rates[paymentMethod.currency_id.name] || 1;
                        const foreignAmount = line.amount * rate;

                        console.log('Adding foreign currency data to receipt:', {
                            payment_method: line.name,
                            local_amount: line.amount,
                            foreign_currency: paymentMethod.currency_id.name,
                            foreign_amount: foreignAmount,
                            rate: rate
                        });

                        return {
                            ...line,
                            payment_currency_id: paymentMethod.currency_id,
                            amount_in_payment_currency: foreignAmount,
                            payment_currency_rate: rate
                        };
                    }
                }

                return line;
            });
        }

        return result;
    }
});
