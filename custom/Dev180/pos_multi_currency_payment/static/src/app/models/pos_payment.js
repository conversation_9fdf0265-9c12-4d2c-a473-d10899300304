import { patch } from "@web/core/utils/patch";
import { PosPayment } from "@point_of_sale/app/models/pos_payment";
import { roundDecimals } from "@web/core/utils/numbers";

patch(PosPayment.prototype, {
    setup(vals) {
        super.setup(...arguments);
        this.payment_currency_id = vals.payment_currency_id || false;
        this.payment_currency_rate = vals.payment_currency_rate || 1.0;
        this.amount_in_payment_currency = vals.amount_in_payment_currency || 0;
    },

    set_amount(value) {
        super.set_amount(value);

        // Update amount in payment currency if applicable
        if (this.payment_currency_id) {
            this.update({
                amount_in_payment_currency: roundDecimals(
                    parseFloat(value) * this.payment_currency_rate || 0,
                    this.payment_currency_id.decimal_places
                ),
            });
        }
    },

    export_for_printing() {
        const result = super.export_for_printing();

        // Add payment currency information if applicable
        if (this.payment_currency_id) {
            result.payment_currency = this.payment_currency_id;
            result.amount_in_payment_currency = this.amount_in_payment_currency;
            result.payment_currency_rate = this.payment_currency_rate;
        }

        return result;
    },
});
