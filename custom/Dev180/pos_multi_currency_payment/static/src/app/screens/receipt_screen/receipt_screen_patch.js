import { patch } from "@web/core/utils/patch";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

patch(ReceiptScreen.prototype, {
    /**
     * Override the onMounted to modify receipt after rendering
     */
    onMounted() {
        super.onMounted();
        // Add a small delay to ensure DOM is fully rendered
        setTimeout(() => {
            this.addForeignCurrencyToReceipt();
        }, 200);
    },

    addForeignCurrencyToReceipt() {
        try {
            const order = this.currentOrder;
            if (!order || !order.payment_ids) return;

            console.log('Adding foreign currency to receipt for order:', order.name);

            // Find all payment lines in the receipt
            const paymentElements = document.querySelectorAll('.paymentlines');

            if (paymentElements.length === 0) {
                console.log('No payment lines found in receipt');
                return;
            }

            // Process each payment
            order.payment_ids.forEach((payment, index) => {
                if (!payment.payment_method_id) return;

                const paymentMethod = payment.payment_method_id;
                console.log('Processing payment:', paymentMethod.name, paymentMethod.use_specific_currency);

                if (paymentMethod.use_specific_currency && paymentMethod.currency_id) {
                    // Calculate foreign currency amount with a test rate
                    const rate = 0.02; // Test rate: 1 LE = 0.02 USD
                    const foreignAmount = payment.amount * rate;

                    console.log('Foreign currency calculation:', {
                        local_amount: payment.amount,
                        rate: rate,
                        foreign_amount: foreignAmount,
                        currency: paymentMethod.currency_id.name
                    });

                    // Find the specific payment line
                    const paymentLine = document.querySelector(`.paymentlines:nth-child(${index + 1})`);
                    if (paymentLine) {
                        // Create foreign currency box
                        const foreignBox = document.createElement('div');
                        foreignBox.className = 'foreign-currency-box';
                        foreignBox.innerHTML = `
                            <div style="border: 2px solid #4CAF50; border-radius: 8px; padding: 8px; margin: 4px 0; background-color: #f8f9fa;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-weight: bold; color: #4CAF50; font-size: 0.9em;">${paymentMethod.currency_id.name}:</span>
                                    <span style="font-weight: bold; color: #2E7D32; font-size: 1.1em;">${foreignAmount.toFixed(2)} ${paymentMethod.currency_id.symbol}</span>
                                </div>
                            </div>
                        `;

                        // Insert after the payment line
                        paymentLine.parentNode.insertBefore(foreignBox, paymentLine.nextSibling);
                        console.log('Foreign currency box added for:', paymentMethod.name);
                    }
                }
            });
        } catch (error) {
            console.error('Error adding foreign currency to receipt:', error);
        }
    }
});
