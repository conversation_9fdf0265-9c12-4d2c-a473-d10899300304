import { patch } from "@web/core/utils/patch";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

patch(ReceiptScreen.prototype, {
    /**
     * Override the onMounted to modify receipt after rendering
     */
    onMounted() {
        super.onMounted();
        // Use requestAnimationFrame for safe DOM manipulation
        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                this.addForeignCurrencyToReceipt();
            });
        });
    },

    addForeignCurrencyToReceipt() {
        try {
            const order = this.currentOrder;
            if (!order || !order.payment_ids) return;

            console.log('Adding foreign currency to receipt for order:', order.name);

            // Find all payment lines in the receipt using a more specific selector
            const receiptContainer = this.el.querySelector('.pos-receipt');
            if (!receiptContainer) {
                console.log('Receipt container not found');
                return;
            }

            const paymentElements = receiptContainer.querySelectorAll('.paymentlines');

            if (paymentElements.length === 0) {
                console.log('No payment lines found in receipt');
                return;
            }

            // Process each payment
            order.payment_ids.forEach((payment, index) => {
                if (!payment.payment_method_id) return;

                const paymentMethod = payment.payment_method_id;
                console.log('Processing payment:', paymentMethod.name, paymentMethod.use_specific_currency);

                if (paymentMethod.use_specific_currency && paymentMethod.currency_id) {
                    // Calculate foreign currency amount with a test rate
                    const rate = 0.02; // Test rate: 1 LE = 0.02 USD
                    const foreignAmount = payment.amount * rate;

                    console.log('Foreign currency calculation:', {
                        local_amount: payment.amount,
                        rate: rate,
                        foreign_amount: foreignAmount,
                        currency: paymentMethod.currency_id.name
                    });

                    // Find the specific payment line
                    const paymentLine = paymentElements[index];
                    if (paymentLine && !paymentLine.querySelector('.foreign-currency-box')) {
                        // Create foreign currency box
                        const foreignBox = document.createElement('div');
                        foreignBox.className = 'foreign-currency-box';

                        // Create the inner structure
                        const innerDiv = document.createElement('div');
                        innerDiv.style.border = '2px solid #4CAF50';
                        innerDiv.style.borderRadius = '8px';
                        innerDiv.style.padding = '8px';
                        innerDiv.style.margin = '4px 0';
                        innerDiv.style.backgroundColor = '#f8f9fa';

                        const contentDiv = document.createElement('div');
                        contentDiv.style.display = 'flex';
                        contentDiv.style.justifyContent = 'space-between';
                        contentDiv.style.alignItems = 'center';

                        const labelSpan = document.createElement('span');
                        labelSpan.style.fontWeight = 'bold';
                        labelSpan.style.color = '#4CAF50';
                        labelSpan.style.fontSize = '0.9em';
                        labelSpan.textContent = paymentMethod.currency_id.name + ':';

                        const amountSpan = document.createElement('span');
                        amountSpan.style.fontWeight = 'bold';
                        amountSpan.style.color = '#2E7D32';
                        amountSpan.style.fontSize = '1.1em';
                        amountSpan.textContent = foreignAmount.toFixed(2) + ' ' + paymentMethod.currency_id.symbol;

                        contentDiv.appendChild(labelSpan);
                        contentDiv.appendChild(amountSpan);
                        innerDiv.appendChild(contentDiv);
                        foreignBox.appendChild(innerDiv);

                        // Insert after the payment line
                        paymentLine.parentNode.insertBefore(foreignBox, paymentLine.nextSibling);
                        console.log('Foreign currency box added for:', paymentMethod.name);
                    }
                }
            });
        } catch (error) {
            console.error('Error adding foreign currency to receipt:', error);
        }
    }
});
