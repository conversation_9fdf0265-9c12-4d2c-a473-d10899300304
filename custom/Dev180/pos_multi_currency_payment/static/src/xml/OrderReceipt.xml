<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
         <xpath expr="//div[@class='after-footer']" position="after">
             <t t-set="paymentlines" t-value="props.data.paymentlines"/>
             <t t-foreach="paymentlines" t-as="line" t-key="line.id or paymentlines.indexOf(line)">
                <div>
                    <t t-if="line.multi_currency_total and line.selected_currency and env.services.pos.currency.name !== line.selected_currency">
                        <div style="border: 2px solid #4CAF50; border-radius: 8px; padding: 8px; margin: 4px 0; background-color: #f8f9fa;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: bold; color: #4CAF50; font-size: 0.9em;">
                                    <t t-esc="line.name"/> (<t t-esc="line.selected_currency"/>):
                                </span>
                                <span style="font-weight: bold; color: #2E7D32; font-size: 1.1em;">
                                    <t t-esc="line.multi_currency_total"/> <t t-esc="line.symbol_of_currency"/>
                                </span>
                            </div>
                        </div>
                        <div style="margin-top: 4px; font-size: 0.8em; color: #666;">
                            Exchange Rate: 1 <t t-esc="env.services.pos.currency.symbol"/> = <t t-esc="line.rate_of_currency"/> <t t-esc="line.symbol_of_currency"/>
                        </div>
                    </t>
                </div>
            </t>
            <hr/>
         </xpath>
     </t>

</templates>
