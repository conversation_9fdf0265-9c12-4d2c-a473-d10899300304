<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
         <xpath expr="//div[@class='after-footer']" position="after">
             <t t-set="paymentlines" t-value="props.data.paymentlines"/>
             <t t-foreach="paymentlines" t-as="line" t-key="line.id or paymentlines.indexOf(line)">
                <div>
                    <t t-if="line.multi_currency_total and line.selected_currency and env.services.pos.currency.name !== line.selected_currency">
                        <t t-esc="line.name"/>
                        <t t-set="currency_symbol_with_name"
                            t-value="'(' + env.services.pos.currency.name + ' ' + env.services.pos.currency.symbol + ')'"/>
                        <span t-esc="currency_symbol_with_name" style="margin-left:30%;"/>--
                        <span t-esc="props.formatCurrency(line.amount)"
                               class="pos-receipt-right-align"/>
                    </t>
                </div>
                <div>
                    <t t-esc="line.name"/>
                    <t t-if="line.multi_currency_total > 0">
                                <t t-set="currency_symbol_with_name"
                                    t-value="'(' + line.symbol_of_currency + ')'"/>
                        <span t-esc="currency_symbol_with_name" style="margin-left:30%;"/>
                        <t t-set="symbol_of_currency_amount"
                                    t-value="'(' + line.symbol_of_currency + ' ' + line.multi_currency_total + ')'"/>
                        <span t-esc="symbol_of_currency_amount"
                              class="pos-receipt-right-align"/>
                     </t>
                    <t t-elif="env.services.pos.config.enable_multi_currency">
                                 <t t-set="currency_symbol_with_name"
                                    t-value="'(' + env.services.pos.currency.name + ' ' + env.services.pos.currency.symbol + ')'"/>
                        <span t-esc="currency_symbol_with_name" style="margin-left:30%;"/>
                        <span t-esc="props.formatCurrency(line.amount, env.services.pos.currency)"
                              class="pos-line-right-align"/>
                             </t>
                    <br/>
                    <t t-if="line.rate_of_currency > 0">
                                  Exchange Rate
                                  <t t-set="currency_symbol_with_name"
                                     t-value="env.services.pos.currency.symbol"/>
                        <t t-set="rate_of_currency"
                           t-value="''+ line.rate_of_currency +  ' ' + line.symbol_of_currency + ''"/>
                        <span t-esc="'' + 1 + ' ' + currency_symbol_with_name + ''" style="margin-left:7%;"/>
                        x
                        <span t-esc="rate_of_currency"/>
                    </t>
                </div>
            </t>
            <hr/>
         </xpath>
     </t>

</templates>
