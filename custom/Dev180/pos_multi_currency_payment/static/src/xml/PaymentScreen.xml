<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="PaymentScreenPaymentLines" t-inherit="point_of_sale.PaymentScreenPaymentLines" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('paymentlines')]" position="replace">
            <div class="paymentlines d-flex flex-column overflow-y-auto gap-1">
               <t t-foreach="props.paymentLines" t-as="line" t-key="line.uuid">
                   <t t-if="line.isSelected()">
                       <div t-attf-class="paymentline selected d-flex align-items-center {{ this.ui.isSmall ? 'bg-100' : 'bg-200'}} border rounded-3"
                            t-att-class="selectedLineClass(line)">
                           <div class="payment-infos d-flex align-items-center justify-content-between flex-grow-1 px-3 py-3 text-truncate cursor-pointer fs-2"
                                t-on-click="() => this.selectLine(line)">
                                <span class="payment-name"><t t-esc="line.payment_method_id.name"/></span>
                               <t t-if="line.multi_currency_total and line.symbol_of_currency">
                                   <div class="multicurrency_payment"
                                        style="
                                            margin: 16px 8px;
                                            padding: 8px 16px;
                                            background-color: #f1f3f5;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            display: inline-block;
                                            font-size: 1rem;
                                            font-weight: 600;
                                            color: #343a40;
                                            text-align: center;
                                            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                                            letter-spacing: 0.3px;
                                            min-width: 100px;
                                        ">
                                       <t t-set="currency_value_symbol"
                                          t-value="line.multi_currency_total + ' ' + line.symbol_of_currency"/>
                                       <t t-esc="currency_value_symbol"/>
                                   </div>
                               </t>
                               <div class="payment-amount"
                                    style="
                                        margin: 16px 8px;
                                        padding: 8px 16px;
                                        background-color: #e9ecef;
                                        border: 1px solid #ced4da;
                                        border-radius: 8px;
                                        display: inline-block;
                                        font-size: 1rem;
                                        font-weight: 600;
                                        color: #212529;
                                        text-align: center;
                                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                                        letter-spacing: 0.3px;
                                        min-width: 100px;
                                    ">
                                   <t t-esc="env.utils.formatCurrency(line.get_amount())"/>
                               </div>
                           </div>
                           <t t-if="!line.payment_status or !['done', 'reversed', 'waitingCard', 'waitingCapture'].includes(line.payment_status)">
                               <button class="delete-button btn btn-link mx-2 px-3"
                                       t-on-click="() => this.props.deleteLine(line.uuid)"
                                       aria-label="Delete" title="Delete">
                                   <i class="oi oi-close text-danger"/>
                               </button>
                           </t>
                       </div>
                   </t>
                   <t t-else="">
                       <div class="paymentline d-flex align-items-center bg-view border rounded-3"
                            t-att-class="unselectedLineClass(line)">
                            <div class="payment-infos d-flex align-items-center justify-content-between flex-grow-1 px-3 py-3 text-truncate cursor-pointer fs-2"
                                 t-on-click="() => this.selectLine(line)">
                                <t t-esc="line.payment_method_id.name"/>
                                <t t-if="line.multi_currency_total and line.symbol_of_currency">
                                    <div class="multicurrency_payment"
                                         style="
                                             margin: 16px 8px;
                                             padding: 8px 16px;
                                             background-color: #f1f3f5;
                                             border: 1px solid #ced4da;
                                             border-radius: 8px;
                                             display: inline-block;
                                             font-size: 1rem;
                                             font-weight: 600;
                                             color: #343a40;
                                             text-align: center;
                                             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                                             letter-spacing: 0.3px;
                                             min-width: 100px;
                                         ">
                                        <t t-set="currency_value_symbol"
                                           t-value="line.multi_currency_total + ' ' + line.symbol_of_currency"/>
                                        <t t-esc="currency_value_symbol"/>
                                    </div>
                                </t>
                                <div class="payment-amount"
                                     style="
                                         margin: 16px 8px;
                                         padding: 8px 16px;
                                         background-color: #e9ecef;
                                         border: 1px solid #ced4da;
                                         border-radius: 8px;
                                         display: inline-block;
                                         font-size: 1rem;
                                         font-weight: 600;
                                         color: #212529;
                                         text-align: center;
                                         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                                         letter-spacing: 0.3px;
                                         min-width: 100px;
                                     ">
                                    <t t-esc="env.utils.formatCurrency(line.get_amount())"/>
                                </div>
                            </div>
                           <t t-if="!line.payment_status or !['done', 'reversed'].includes(line.payment_status)">
                               <div class="delete-button delete-button btn btn-link mx-2 px-3"
                                    t-on-click="() => this.props.deleteLine(line.uuid)"
                                    aria-label="Delete" title="Delete">
                                   <i class="oi oi-close text-danger"/>
                               </div>
                           </t>
                       </div>
                   </t>
               </t>
           </div>
        </xpath>
    </t>

</templates>
