<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Override the OrderReceipt template to include multi-currency payment info -->
    <t t-name="pos_multi_currency_payment.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <!-- Replace the payment lines section -->
        <xpath expr="//div[@class='paymentlines text-start']" position="replace">
            <div class="paymentlines text-start" t-foreach="props.data.paymentlines" t-as="line" t-key="line_index">
                <!-- Payment method name and local currency amount -->
                <div class="payment-line-local">
                    <t t-esc="line.name" />
                    <span t-esc="props.formatCurrency(line.amount)" class="pos-receipt-right-align"/>
                </div>

                <!-- Check if payment has foreign currency -->
                <t t-if="line.payment_currency_id and line.amount_in_payment_currency and line.amount_in_payment_currency != line.amount">
                    <!-- Foreign currency line in a bordered box -->
                    <div class="payment-amount-foreign-box">
                        <div class="foreign-currency-content">
                            <span class="foreign-currency-label">
                                <t t-esc="line.payment_currency_id.name" />:
                            </span>
                            <span class="foreign-currency-amount">
                                <t t-esc="line.amount_in_payment_currency.toFixed(2)" />
                                <t t-esc="line.payment_currency_id.symbol" />
                            </span>
                        </div>
                    </div>
                </t>
            </div>
        </xpath>
    </t>

</templates>
