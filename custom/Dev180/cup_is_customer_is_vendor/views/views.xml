<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit Is Customer and Is Vendor -->
        <record id="view_partner_form_inherit" model="ir.ui.view">
            <field name="name">view_partner_form_inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='sales_purchases']/group/group[@name='sale']/field[@name='user_id']" position="before">
                    <field name="is_customer"/>
                </xpath>
                <xpath expr="//page[@name='sales_purchases']/group/group[@name='purchase']/field[@name='property_supplier_payment_term_id']" position="before">
                    <field name="is_supplier"/>
                </xpath>
            </field>
        </record>

        <!-- Inherit Action -->
        <record id="account.res_partner_action_customer" model="ir.actions.act_window">
            <field name="name">Customers</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{'search_default_customer': 1,'res_partner_search_mode': 'customer', 'default_is_company': True, 'default_customer_rank': 1, 'default_is_customer': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new customer in your address book
                </p><p>
                    Odoo helps you easily track all activities related to a customer.
                </p>
            </field>
        </record>
        <record id="account.res_partner_action_supplier" model="ir.actions.act_window">
            <field name="name">Vendors</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_supplier': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new supplier in your address book
                </p><p>
                    Odoo helps you easily track all activities related to a supplier.
                </p>
            </field>
        </record>

        <!-- Inherit Search -->
        <record id="res_partner_view_search" model="ir.ui.view">
            <field name="name">res_partner_view_search</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='customer']" position="replace">
                    <filter string="Customer Invoices" name="customer" domain="['|',('customer_rank','>', 0),('is_customer','=',True)]"/>
                </xpath>
                <xpath expr="//filter[@name='supplier']" position="replace">
                    <filter string="Vendor Bills" name="supplier" domain="['|',('supplier_rank','>', 0),('is_supplier','=',True)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>