<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <!-- Simple and safe multi-currency receipt -->
    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('paymentlines')]" position="after">
            <!-- Add foreign currency info after payment lines -->
            <t t-foreach="props.data.paymentlines" t-as="line" t-key="'foreign_' + (line.id or props.data.paymentlines.indexOf(line))">
                <t t-if="line.symbol_of_currency and line.multi_currency_total and line.symbol_of_currency != env.services.pos.currency.symbol">
                    <div class="pos-payment-terminal-receipt-line" style="font-style: italic; color: #666; margin-left: 20px;">
                        Foreign Amount: <t t-esc="line.multi_currency_total || 0"/> <t t-esc="line.symbol_of_currency || ''"/>
                    </div>
                    <t t-if="line.rate_of_currency and line.rate_of_currency != 1">
                        <div class="pos-payment-terminal-receipt-line" style="font-size: 0.9em; color: #888; margin-left: 20px;">
                            Rate: 1 <t t-esc="env.services.pos.currency.symbol"/> = <t t-esc="line.rate_of_currency || 0"/> <t t-esc="line.symbol_of_currency || ''"/>
                        </div>
                    </t>
                </t>
            </t>
        </xpath>
     </t>

</templates>
