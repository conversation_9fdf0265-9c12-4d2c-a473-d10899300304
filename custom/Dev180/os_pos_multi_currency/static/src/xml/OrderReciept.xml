<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <!-- Override payment lines section to show multi-currency amounts -->
        <xpath expr="//div[hasclass('paymentlines')]" position="replace">
            <div class="paymentlines">
                <t t-foreach="props.data.paymentlines" t-as="line" t-key="line.id or props.data.paymentlines.indexOf(line)">
                    <!-- Local Currency Payment Line -->
                    <div class="pos-payment-terminal-receipt-line">
                        <t t-esc="line.name"/>
                        <span t-esc="props.formatCurrency(line.amount)" class="pos-receipt-right-align"/>
                    </div>

                    <!-- Foreign Currency Payment Line (if exists) -->
                    <t t-if="line.symbol_of_currency and line.multi_currency_total and line.symbol_of_currency != env.services.pos.currency.symbol">
                        <div class="pos-payment-terminal-receipt-line" style="font-style: italic; color: #666;">
                            <t t-esc="line.name"/> (<t t-esc="line.symbol_of_currency"/>)
                            <span class="pos-receipt-right-align">
                                <t t-esc="parseFloat(line.multi_currency_total).toFixed(2)"/> <t t-esc="line.symbol_of_currency"/>
                            </span>
                        </div>
                    </t>

                    <!-- Exchange Rate (if foreign currency used) -->
                    <t t-if="line.rate_of_currency and line.rate_of_currency != 1 and line.symbol_of_currency">
                        <div class="pos-payment-terminal-receipt-line" style="font-size: 0.9em; color: #888;">
                            Exchange Rate: 1 <t t-esc="env.services.pos.currency.symbol"/> = <t t-esc="line.rate_of_currency"/> <t t-esc="line.symbol_of_currency"/>
                        </div>
                    </t>
                </t>
            </div>
        </xpath>
     </t>

</templates>
