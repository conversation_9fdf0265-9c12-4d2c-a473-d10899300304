<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <!-- Multi-currency receipt with proper formatting -->
    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('paymentlines')]" position="after">
            <!-- Add foreign currency info after payment lines -->
            <t t-foreach="props.data.paymentlines" t-as="line" t-key="'foreign_' + (line.id or props.data.paymentlines.indexOf(line))">
                <t t-if="line.symbol_of_currency and line.multi_currency_total and line.symbol_of_currency != env.services.pos.currency.symbol and line.multi_currency_total > 0">
                    <!-- Foreign Currency Amount Line -->
                    <div class="pos-payment-terminal-receipt-line" style="font-style: italic; color: #666;">
                        <t t-esc="line.name"/> (<t t-esc="line.symbol_of_currency"/>)
                        <span class="pos-receipt-right-align">
                            <t t-esc="parseFloat(line.multi_currency_total).toFixed(2)"/> <t t-esc="line.symbol_of_currency"/>
                        </span>
                    </div>

                    <!-- Exchange Rate Line -->
                    <t t-if="line.rate_of_currency and line.rate_of_currency > 0 and line.rate_of_currency != 1">
                        <div class="pos-payment-terminal-receipt-line" style="font-size: 0.9em; color: #888;">
                            Exchange Rate
                            <span class="pos-receipt-right-align">
                                1 <t t-esc="env.services.pos.currency.symbol"/> = <t t-esc="parseFloat(line.rate_of_currency).toFixed(6)"/> <t t-esc="line.symbol_of_currency"/>
                            </span>
                        </div>
                    </t>
                </t>
            </t>
        </xpath>
     </t>

</templates>
