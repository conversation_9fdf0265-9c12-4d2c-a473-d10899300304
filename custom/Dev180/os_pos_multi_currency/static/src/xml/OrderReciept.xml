<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <!-- Temporarily disabled to prevent white screen -->
    <!--
    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('paymentlines')]" position="after">
            <t t-foreach="props.data.paymentlines" t-as="line" t-key="'foreign_' + props.data.paymentlines.indexOf(line)">
                <t t-if="line.symbol_of_currency and line.multi_currency_total">
                    <t t-if="line.symbol_of_currency != env.services.pos.currency.symbol">
                        <div class="pos-payment-terminal-receipt-line" style="font-style: italic;">
                            <t t-esc="line.name"/> (<t t-esc="line.symbol_of_currency"/>)
                            <span class="pos-receipt-right-align">
                                <t t-esc="line.multi_currency_total"/> <t t-esc="line.symbol_of_currency"/>
                            </span>
                        </div>

                        <t t-if="line.rate_of_currency">
                            <div class="pos-payment-terminal-receipt-line" style="font-size: 0.9em;">
                                Exchange Rate: 1 <t t-esc="env.services.pos.currency.symbol"/> = <t t-esc="line.rate_of_currency"/> <t t-esc="line.symbol_of_currency"/>
                            </div>
                        </t>
                    </t>
                </t>
            </t>
        </xpath>
     </t>
     -->

</templates>
