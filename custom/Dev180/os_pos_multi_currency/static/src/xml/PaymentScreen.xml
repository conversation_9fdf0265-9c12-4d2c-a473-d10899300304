<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

     <t t-name="PaymentScreenButtons" t-inherit="point_of_sale.PaymentScreenButtons" t-inherit-mode="extension">
         <xpath expr="//div[hasclass('payment-buttons')]" position="inside">
             <button class="btn btn-primary currency-btn d-flex align-items-center justify-content-center gap-2 py-2 px-3 rounded-pill shadow-sm"
                     t-on-click="onClickMultiCurrencyPopup">
                <i class="fa fa-money fs-5" role="img" aria-hidden="true"/>
                 <span class="fw-medium">Multi-Currencies</span>
            </button>
         </xpath>
     </t>

    <t t-name="PaymentScreenPaymentLines" t-inherit="point_of_sale.PaymentScreenPaymentLines" t-inherit-mode="extension"
       owl="1">
         <xpath expr="//div[hasclass('paymentlines')]" position="replace">
             <div class="paymentlines d-flex flex-column overflow-y-auto gap-1">
                <t t-foreach="props.paymentLines" t-as="line" t-key="line.uuid">
                    <t t-if="line.isSelected()">
                        <div t-attf-class="paymentline selected d-flex align-items-center {{ this.ui.isSmall ? 'bg-100' : 'bg-200'}} border rounded-3"
                             t-att-class="selectedLineClass(line)">
                            <div class="payment-infos d-flex align-items-center justify-content-between flex-grow-1 px-3 py-3 text-truncate cursor-pointer fs-2"
                                 t-on-click="() => this.selectLine(line)">
                                 <span class="payment-name"><t t-esc="line.payment_method_id.name"/></span>
                                <t t-if="line.symbol_of_currency and line.symbol_of_currency != env.services.pos.currency.symbol">
<div class="multicurrency_payment"
     style="
         margin: 16px 8px;
         padding: 8px 16px;
         background-color: #f1f3f5;
         border: 1px solid #ced4da;
         border-radius: 8px;
         display: inline-block;
         font-size: 1rem;
         font-weight: 600;
         color: #343a40;
         text-align: center;
         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
         letter-spacing: 0.3px;
         min-width: 100px;
     ">
    <t t-set="foreign_amount" t-value="line.multi_currency_total || line.get_converted_amount() || 0"/>
    <t t-set="foreign_symbol" t-value="line.symbol_of_currency || line.get_currency_symbol() || ''"/>
    <t t-esc="foreign_amount + ' ' + foreign_symbol"/>
</div>
             </t>
                                <div class="payment-amount"
                                     style="
                             margin: 16px 8px;
                             padding: 8px 16px;
                             background-color: #e9ecef;
                             border: 1px solid #ced4da;
                             border-radius: 8px;
                             display: inline-block;
                             font-size: 1rem;
                             font-weight: 600;
                             color: #212529;
                             text-align: center;
                             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                             letter-spacing: 0.3px;
                             min-width: 100px;
                         ">
                        <t t-esc="env.utils.formatCurrency(line.get_amount())"/>
                    </div>
                            </div>
                            <t t-if="!line.payment_status or !['done', 'reversed', 'waitingCard', 'waitingCapture'].includes(line.payment_status)">
                                <button class="delete-button btn btn-link mx-2 px-3"
                                        t-on-click="() => this.props.deleteLine(line.uuid)"
                                        aria-label="Delete" title="Delete">
                                    <i class="oi oi-close text-danger"/>
                                </button>
                            </t>
                            <t t-elif="line.payment_status and ['waitingCard', 'waitingCapture'].includes(line.payment_status)">
                                <div class="mx-2 px-3">
                                    <i class="fa fa-circle-o-notch fa-spin" role="img"/>
                                </div>
                            </t>
                        </div>
                        <t t-if="line and line.payment_status">
                            <div t-attf-class="{{ this.ui.isSmall ? 'bg-100' : 'bg-200'}} paymentline electronic_payment">
                                <t t-if="line.payment_status == 'pending'">
                                    <div class="electronic_status">
                                        Payment request pending
                                    </div>
                                    <div class="button send_payment_request highlight text-bg-primary"
                                         title="Send Payment Request"
                                         t-on-click="() => this.props.sendPaymentRequest(line)">
                                        Send
                                    </div>
                                </t>
                                <t t-elif="line.payment_status == 'retry'">
                                    <div class="electronic_status">
                                        Transaction cancelled
                                    </div>
                                    <div class="button send_payment_request highlight text-bg-primary"
                                         title="Send Payment Request"
                                         t-on-click="() => this.props.sendPaymentRequest(line)">
                                        Retry
                                    </div>
                                </t>
                                <t t-elif="line.payment_status == 'force_done'">
                                    <div class="electronic_status">
                                        Connection error
                                    </div>
                                    <div class="button send_force_done" title="Force Done"
                                         t-on-click="() => this.props.sendForceDone(line)">
                                        Force done
                                    </div>
                                </t>
                                <t t-elif="line.payment_status == 'waitingCard'">
                                    <div class="electronic_status">
                                        Waiting for card
                                    </div>
                                    <div class="button send_payment_cancel" title="Cancel Payment Request"
                                         t-on-click="() => this.props.sendPaymentCancel(line)">
                                        Cancel
                                    </div>
                                </t>
                                <t t-elif="['waiting', 'waitingCancel', 'waitingCapture'].includes(line.payment_status)">
                                    <div class="electronic_status">
                                        Request sent
                                    </div>
                                    <div>
                                        <i class="fa fa-circle-o-notch fa-spin" role="img"/>
                                    </div>
                                </t>
                                <t t-elif="line.payment_status == 'reversing'">
                                    <div class="electronic_status">
                                        Reversal request sent to terminal
                                    </div>
                                    <div>
                                        <i class="fa fa-circle-o-notch fa-spin" role="img"/>
                                    </div>
                                </t>
                                <t t-elif="line.payment_status == 'done'">
                                    <div class="electronic_status">
                                        Payment Successful
                                    </div>
                                    <t t-if="line.can_be_reversed">
                                        <div class="button send_payment_reversal" title="Reverse Payment"
                                             t-on-click="() => this.props.sendPaymentReverse(line)">
                                            Reverse
                                        </div>
                                    </t>
                                    <t t-else="">
                                        <div></div>
                                    </t>
                                </t>
                                <t t-elif="line.payment_status == 'reversed'">
                                    <div class="electronic_status">
                                        Payment reversed
                                    </div>
                                    <div></div>
                                </t>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <div class="paymentline d-flex align-items-center bg-view border rounded-3"
                             t-att-class="unselectedLineClass(line)">
                             <div class="payment-infos d-flex align-items-center justify-content-between flex-grow-1 px-3 py-3 text-truncate cursor-pointer fs-2"
                                  t-on-click="() => this.selectLine(line)">
                                 <t t-esc="line.payment_method_id.name"/>
                                 <t t-if="line.symbol_of_currency and line.symbol_of_currency != env.services.pos.currency.symbol">
<div class="multicurrency_payment"
     style="
         margin: 16px 8px;
         padding: 8px 16px;
         background-color: #f1f3f5;
         border: 1px solid #ced4da;
         border-radius: 8px;
         display: inline-block;
         font-size: 1rem;
         font-weight: 600;
         color: #343a40;
         text-align: center;
         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
         letter-spacing: 0.3px;
         min-width: 100px;
     ">
    <t t-set="foreign_amount" t-value="line.multi_currency_total || line.get_converted_amount() || 0"/>
    <t t-set="foreign_symbol" t-value="line.symbol_of_currency || line.get_currency_symbol() || ''"/>
    <t t-esc="foreign_amount + ' ' + foreign_symbol"/>
</div>
             </t>
                                 <div class="payment-amount"
                                      style="
                             margin: 16px 8px;
                             padding: 8px 16px;
                             background-color: #e9ecef;
                             border: 1px solid #ced4da;
                             border-radius: 8px;
                             display: inline-block;
                             font-size: 1rem;
                             font-weight: 600;
                             color: #212529;
                             text-align: center;
                             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                             letter-spacing: 0.3px;
                             min-width: 100px;
                         ">
                        <t t-esc="env.utils.formatCurrency(line.get_amount())"/>
                    </div>
                             </div>
                            <t t-if="!line.payment_status or !['done', 'reversed'].includes(line.payment_status)">
                                <div class="delete-button delete-button btn btn-link mx-2 px-3"
                                     t-on-click="() => this.props.deleteLine(line.uuid)"
                                     aria-label="Delete" title="Delete">
                                    <i class="oi oi-close text-danger"/>
                                </div>
                            </t>
                        </div>
                    </t>
                </t>
            </div>

         </xpath>

    </t>

</templates>
