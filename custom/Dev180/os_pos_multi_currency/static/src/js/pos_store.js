/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";
import { useService } from "@web/core/utils/hooks";
//import { Payment } from "@point_of_sale/app/store/models";
import { PosPayment } from "@point_of_sale/app/models/pos_payment";
// import { useState } from "@odoo/owl";

patch(PosStore.prototype, {

    async processServerData() {
        await super.processServerData();

        // Check if multi-currency is enabled and currencies are configured
        if (this.config.pos_multi_currency_payment && this.config.pos_payment_currency_ids) {
            try {
                this.multi_currency_data = await this.data.orm.call(
                    "pos.config",
                    "fetch_multi_currency_data",
                    [this.config.id],
                    {
                        context: {
                            check_from_pos: true,
                        },
                    }
                );

                // Log currency data for debugging
                console.log("Multi-currency data loaded:", this.multi_currency_data);

            } catch (error) {
                console.error("Error fetching multi-currency data:", error);
                this.multi_currency_data = [];
            }
        } else {
            this.multi_currency_data = [];
        }
    },

    async refreshCurrencyRates() {
        // Refresh currency rates from server
        if (this.config.pos_multi_currency_payment && this.config.pos_payment_currency_ids) {
            try {
                const currency_ids = this.config.pos_payment_currency_ids;
                const updated_rates = await this.data.orm.call(
                    "pos.config",
                    "get_current_currency_rates",
                    [currency_ids],
                );

                // Update existing currency data with new rates
                this.multi_currency_data.forEach(currency => {
                    if (updated_rates[currency.id]) {
                        currency.rate = updated_rates[currency.id].rate;
                        currency.inverse_rate = updated_rates[currency.id].inverse_rate;
                    }
                });

                console.log("Currency rates refreshed:", updated_rates);
                return true;
            } catch (error) {
                console.error("Error refreshing currency rates:", error);
                return false;
            }
        }
        return false;
    },

})

patch(PosPayment.prototype, {
    setup(obj, options) {
        super.setup(...arguments);
        this.selected_currency = this.selected_currency || false;
        this.rate_of_currency = this.rate_of_currency || 0.0
        this.symbol_of_currency = this.symbol_of_currency || 0.0
        this.multi_currency_total = this.multi_currency_total || 0.0
    },


    get_selected_currency() {
        return this.selected_currency || '';
    },



    get_currency_symbol() {
        return this.symbol_of_currency || '';
    },

    get_currency_rate() {
        return this.rate_of_currency || 0.0;
    },


    get_converted_amount() {
        // If we have multi_currency_total, use it directly
        if (this.multi_currency_total && this.multi_currency_total > 0) {
            return parseFloat(this.multi_currency_total).toFixed(2);
        }

        // Otherwise calculate from rate and amount
        if (this.rate_of_currency && this.amount) {
            const convertedAmount = (this.rate_of_currency * this.amount).toFixed(2);
            return convertedAmount;
        }

        return this.amount ? parseFloat(this.amount).toFixed(2) : "0.00";
    },

    get_currency_amount_paid() {
        return this.multi_currency_total ? Number(this.multi_currency_total) : 0.0;
    },

    init_from_JSON(json) {
        super.init_from_JSON(...arguments);
        this.selected_currency = json.selected_currency || '';
        this.symbol_of_currency = json.symbol_of_currency || '';
        this.rate_of_currency = json.rate_of_currency || 0.0;
        this.multi_currency_total = json.multi_currency_total || 0.0;
        this.inverse_rate = json.inverse_rate || 0.0;
    },

    export_as_JSON() {
        let json = super.export_as_JSON(...arguments);
        json.selected_currency = this.get_selected_currency();
        json.symbol_of_currency = this.get_currency_symbol();
        json.rate_of_currency = this.get_currency_rate() || 0.0;
        json.multi_currency_total = this.get_currency_amount_paid() || 0.0;
        json.inverse_rate = this.inverse_rate || 0.0;
        return json;
    },

    export_for_printing() {
        var receipt = super.export_for_printing(...arguments);

        // Add multi-currency data safely with proper values
        receipt.selected_currency = this.selected_currency || '';
        receipt.symbol_of_currency = this.symbol_of_currency || '';
        receipt.rate_of_currency = this.rate_of_currency || 0;
        receipt.multi_currency_total = this.multi_currency_total || 0;
        receipt.inverse_rate = this.inverse_rate || 0;

        // Debug log to check values
        console.log('Payment export_for_printing:', {
            name: receipt.name,
            amount: receipt.amount,
            symbol_of_currency: receipt.symbol_of_currency,
            rate_of_currency: receipt.rate_of_currency,
            multi_currency_total: receipt.multi_currency_total
        });

        return receipt;
    }
});
