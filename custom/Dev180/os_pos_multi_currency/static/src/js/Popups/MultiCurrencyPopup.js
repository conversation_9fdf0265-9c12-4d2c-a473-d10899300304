/** @odoo-module **/

import { Dialog } from "@web/core/dialog/dialog";
import { Component, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class MultiCurrencyPopup extends Component {
    static components = { Dialog };
    static template = "os_pos_multi_currency.MultiCurrencyPopup";


    static props = {
        close: Function,
        title: { type: String, optional: true },
        confirm: { type: Function, optional: true },
        cancel: { type: Function, optional: true },
        payment_method: { type: Array, optional: true },
        getPayload: { type: Function, optional: true },
    };


    static defaultProps = {
        title: "Multi-Currency Payment",
    };

    setup() {
        const MultiCurrencies = this.env.services.pos.multi_currency_data || [];
        if (MultiCurrencies.length === 0) {
            console.error("Multi Currencies not found!");
        }

        // Log currency data for debugging
        console.log("Available currencies in popup:", MultiCurrencies);

        // Get default currency info
        const defaultCurrency = this.env.services.pos.currency || {};
        const firstCurrency = MultiCurrencies.length > 0 ? MultiCurrencies[0] : {};

        // Validate first currency rates
        if (firstCurrency.rate && firstCurrency.rate !== 0) {
            console.log(`First currency ${firstCurrency.name} rate: ${firstCurrency.rate}, inverse: ${firstCurrency.inverse_rate}`);
        } else {
            console.warn(`Invalid rate for first currency ${firstCurrency.name}:`, firstCurrency.rate);
        }

        this.dialog = useService("dialog");
        this.state = useState({
            values: MultiCurrencies,
            default_currency: defaultCurrency,
            selected_curr_name: firstCurrency.name || defaultCurrency.name || "",
            rate_of_currency: parseFloat(firstCurrency.rate) || 1,
            inverse_rate: parseFloat(firstCurrency.inverse_rate) || 1,
            symbol: firstCurrency.symbol || defaultCurrency.symbol || "",
            AmountTotal: this.getDueAmount(),
            amount_total_currency: 0,
        });

        if (this.env.services.pos.config.cash_rounding) {
            const cash_rounding = this.env.services.pos.cash_rounding?.[0]?.rounding || 1;
            this.state.AmountTotal = this.roundPr(this.env.services.pos.get_order().get_due(), cash_rounding);
        }

        this.state.amount_total_currency = this.calculateTotalCurrency();

        // Log final calculation
        console.log(`Amount: ${this.state.AmountTotal}, Rate: ${this.state.rate_of_currency}, Total: ${this.state.amount_total_currency}`);
    }

    getDueAmount() {
        try {
            const order = this.env.services.pos.get_order();
            const paymentline = order?.get_selected_paymentline();
            return paymentline?.amount || order?.get_due() || 0;
        } catch (error) {
            console.error("Error getting due amount:", error);
            return 0;
        }
    }

    calculateTotalCurrency() {
        const rate = parseFloat(this.state.rate_of_currency) || 1;
        const amount = parseFloat(this.state.AmountTotal) || 0;
        return (rate * amount).toFixed(2);
    }

    onCurrencyButtonClick(event) {
        const selectedValue = this.state.values.find((val) => val.id === parseFloat(event));
        if (selectedValue) {
            // Validate rates before using them
            const rate = parseFloat(selectedValue.rate) || 1;
            const inverse_rate = parseFloat(selectedValue.inverse_rate) || 1;

            console.log(`Selected currency: ${selectedValue.name}, Rate: ${rate}, Inverse: ${inverse_rate}`);

            this.state.selected_currency_id = selectedValue.id;
            this.state.selected_curr_name = selectedValue.name;
            this.state.rate_of_currency = rate;
            this.state.inverse_rate = inverse_rate;
            this.state.symbol = selectedValue.symbol;
            this.state.amount_total_currency = this.calculateTotalCurrency();

            console.log(`Calculated amount in ${selectedValue.name}: ${this.state.amount_total_currency}`);
        }
        this.confirm()
    }

    onPaymentMethodButtonClick(paymentMethodId) {
    this.state.selected_payment_method_id = paymentMethodId;
    }



    roundPr(value, rounding) {
        return Math.round(value / rounding) * rounding;
    }

    confirm() {

        const payload = this.getPayload ? this.getPayload() : {
            currency_name: this.state.selected_curr_name,
            rate_of_currency: this.state.rate_of_currency,
            inverse_rate: this.state.inverse_rate,
            symbol: this.state.symbol,
            selected_payment_method_id : this.state.selected_payment_method_id
        };
        this.props.confirm({ confirmed: true, payload });
        this.props.close();
    }


    cancel() {
        if (this.props.cancel) {
            this.props.cancel();
        } else {
            this.props.close();
        }
    }

    getPayload() {
        const payload = {
            currency_name: this.state.selected_curr_name,
            selected_payment_method_id: this.state.selected_payment_method_id,
            amount_total_currency: parseFloat(this.state.amount_total_currency) || 0,
            rate_of_currency: parseFloat(this.state.rate_of_currency) || 1,
            inverse_rate: parseFloat(this.state.inverse_rate) || 1,
            symbol: this.state.symbol,
        };
        console.log('Multi-currency payload:', payload);
        return payload;
    }
}
