/** @odoo-module **/

import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";
import { registry } from "@web/core/registry";
import { WarningDialog } from "@web/core/errors/error_dialogs";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";
import { MultiCurrencyPopup } from "../Popups/MultiCurrencyPopup"
import { makeAwaitable } from "@point_of_sale/app/store/make_awaitable_dialog";
import { _t } from "@web/core/l10n/translation";
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";

patch(PaymentScreen.prototype, {
    setup() {
        super.setup(...arguments);
        this.dialog = useService("dialog");
    },
    async onClickMultiCurrencyPopup() {

        if (this.pos.multi_currency_data.length > 0) {
                    if (!this.selectedPaymentLine) {
                return this.dialog.add(ConfirmationDialog, {
                    title: _t("No Payment Method Found !!!"),
                    body: _t("Please Choose Payment Method For Multi-Currency Payment."),
                });
                } //

                // Debug: Check current order and payment line
                console.log("Current order:", this.currentOrder);
                console.log("Selected payment line:", this.selectedPaymentLine);
                console.log("Order due:", this.currentOrder.get_due());
                console.log("Order total:", this.currentOrder.get_total_with_tax());
                console.log("Payment line amount:", this.selectedPaymentLine.amount);
            const payment_method_data = this.payment_methods_from_config.map((id) => id);
            const dialogPopup = await makeAwaitable(this.dialog, MultiCurrencyPopup, {
                payment_method: payment_method_data,
                title: _t("Choose Currency"),
                confirm: async ({ confirmed, payload }) => {
                    if (confirmed) {
                            var paymentLine = this.currentOrder.get_selected_paymentline();

                            console.log('Payment line before update:', {
                                amount: paymentLine.amount,
                                multi_currency_total: paymentLine.multi_currency_total,
                                rate_of_currency: paymentLine.rate_of_currency
                            });

                            console.log('Payload received:', payload);

                            // Set multi-currency values with proper precision
                            paymentLine.multi_currency_total = parseFloat(payload.amount_total_currency) || 0;
                            paymentLine.selected_currency = payload.currency_name;
                            paymentLine.selected_currency_symbol = payload.symbol;
                            paymentLine.rate_of_currency = parseFloat(payload.rate_of_currency) || 1;
                            paymentLine.symbol_of_currency = payload.symbol;
                            paymentLine.inverse_rate = parseFloat(payload.inverse_rate) || 1;

                            console.log('Payment line after update:', {
                                amount: paymentLine.amount,
                                multi_currency_total: paymentLine.multi_currency_total,
                                rate_of_currency: paymentLine.rate_of_currency,
                                symbol_of_currency: paymentLine.symbol_of_currency
                            });

                            // Force UI update
                            this.render();
                    }
                },
            });
        } else {
            this.dialog.add(ConfirmationDialog, {
                title: _t("Currency Not Configured"),
                body: _t("Please Configure The Currency For Multi-Currency Payment."),
            });
        }
    },

    updateSelectedPaymentline(amount = false) {
        if (this.paymentLines.every((line) => line.paid)) {
            this.currentOrder.add_paymentline(this.payment_methods_from_config[0]);
        }
        if (!this.selectedPaymentLine) {
            return;
        } // do nothing if no selected payment line
        if (amount === false) {
            if (this.numberBuffer.get() === null) {
                amount = null;
            } else if (this.numberBuffer.get() === "") {
                amount = 0;
            } else {
                amount = this.numberBuffer.getFloat();
            }
        }
        // disable changing amount on paymentlines with running or done payments on a payment terminal
        const payment_terminal = this.selectedPaymentLine.payment_method_id.payment_terminal;
        const hasCashPaymentMethod = this.payment_methods_from_config.some(
            (method) => method.type === "cash"
        );
        if (
            !hasCashPaymentMethod &&
            amount > this.currentOrder.get_due() + this.selectedPaymentLine.amount
        ) {
            this.selectedPaymentLine.set_amount(0);
            this.numberBuffer.set(this.currentOrder.get_due().toString());
            amount = this.currentOrder.get_due();
            this.showMaxValueError();
        }
        if (
            payment_terminal &&
            !["pending", "retry"].includes(this.selectedPaymentLine.get_payment_status())
        ) {
            return;
        }
        if (amount === null) {
            this.deletePaymentLine(this.selectedPaymentLine.uuid);
        } else {
            if (this.selectedPaymentLine.inverse_rate && this.selectedPaymentLine.rate_of_currency){
                // If we have foreign currency set, calculate properly
                // amount is in local currency, convert to foreign currency for display
                const foreignAmount = amount * this.selectedPaymentLine.rate_of_currency;
                this.selectedPaymentLine.multi_currency_total = foreignAmount;
                this.selectedPaymentLine.set_amount(amount);

                console.log(`Updated payment: Local=${amount}, Foreign=${foreignAmount}, Rate=${this.selectedPaymentLine.rate_of_currency}`);
            } else {
                // No foreign currency, just set the amount
                this.selectedPaymentLine.set_amount(amount);
            }
        }
    }
})
