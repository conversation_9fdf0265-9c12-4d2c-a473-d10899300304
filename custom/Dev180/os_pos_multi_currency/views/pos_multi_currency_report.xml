<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Multi Currency Sales Report Action -->
    <record id="action_pos_multi_currency_report" model="ir.actions.act_window">
        <field name="name">Multi Currency Sales Report</field>
        <field name="res_model">pos.order</field>
        <field name="view_mode">pivot,graph,list</field>
        <field name="domain">[('state', 'in', ['paid', 'done', 'invoiced'])]</field>
        <field name="context">{
            'search_default_group_by_currency': 1,
            'search_default_group_by_date': 1,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Multi Currency Sales Report
            </p>
            <p>
                This report shows sales grouped by currency used in payments.
            </p>
        </field>
    </record>

    <!-- Multi Currency Sales Report Search View -->
    <record id="view_pos_order_search_multi_currency" model="ir.ui.view">
        <field name="name">pos.order.search.multi.currency</field>
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_order_search"/>
        <field name="arch" type="xml">
            <filter name="group_by_config" position="after">
                <filter name="group_by_currency" string="Currency Used" 
                        domain="[]" context="{'group_by': 'primary_currency_symbol'}"/>
            </filter>
        </field>
    </record>

    <!-- Multi Currency Sales Report Pivot View -->
    <record id="view_pos_order_pivot_multi_currency" model="ir.ui.view">
        <field name="name">pos.order.pivot.multi.currency</field>
        <field name="model">pos.order</field>
        <field name="arch" type="xml">
            <pivot string="Multi Currency Sales Analysis">
                <field name="date_order" type="row" interval="month"/>
                <field name="primary_currency_symbol" type="row"/>
                <field name="config_id" type="col"/>
                <field name="amount_total" type="measure"/>
                <field name="amount_paid" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Multi Currency Sales Report Graph View -->
    <record id="view_pos_order_graph_multi_currency" model="ir.ui.view">
        <field name="name">pos.order.graph.multi.currency</field>
        <field name="model">pos.order</field>
        <field name="arch" type="xml">
            <graph string="Multi Currency Sales Analysis" type="bar">
                <field name="primary_currency_symbol"/>
                <field name="amount_total" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Multi Currency Payment Report Action -->
    <record id="action_pos_payment_multi_currency_report" model="ir.actions.act_window">
        <field name="name">Multi Currency Payment Report</field>
        <field name="res_model">pos.payment</field>
        <field name="view_mode">pivot,graph,list</field>
        <field name="context">{
            'search_default_group_by_currency': 1,
            'search_default_group_by_payment_method': 1,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Multi Currency Payment Report
            </p>
            <p>
                This report shows payments grouped by currency and payment method.
            </p>
        </field>
    </record>

    <!-- Multi Currency Payment Report Search View -->
    <record id="view_pos_payment_search_multi_currency" model="ir.ui.view">
        <field name="name">pos.payment.search.multi.currency</field>
        <field name="model">pos.payment</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_payment_search"/>
        <field name="arch" type="xml">
            <filter name="group_by_payment_method" position="after">
                <filter name="group_by_currency" string="Currency" 
                        domain="[]" context="{'group_by': 'symbol_of_currency'}"/>
            </filter>
        </field>
    </record>

    <!-- Multi Currency Payment Report Pivot View -->
    <record id="view_pos_payment_pivot_multi_currency" model="ir.ui.view">
        <field name="name">pos.payment.pivot.multi.currency</field>
        <field name="model">pos.payment</field>
        <field name="arch" type="xml">
            <pivot string="Multi Currency Payment Analysis">
                <field name="payment_date" type="row" interval="month"/>
                <field name="symbol_of_currency" type="row"/>
                <field name="payment_method_id" type="col"/>
                <field name="amount" type="measure"/>
                <field name="multi_currency_total" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_pos_multi_currency_reports" 
              name="Multi Currency Reports" 
              parent="point_of_sale.menu_point_rep" 
              sequence="10"/>

    <menuitem id="menu_pos_multi_currency_sales_report" 
              name="Sales by Currency" 
              parent="menu_pos_multi_currency_reports" 
              action="action_pos_multi_currency_report" 
              sequence="1"/>

    <menuitem id="menu_pos_multi_currency_payment_report" 
              name="Payments by Currency" 
              parent="menu_pos_multi_currency_reports" 
              action="action_pos_payment_multi_currency_report" 
              sequence="2"/>

</odoo>
