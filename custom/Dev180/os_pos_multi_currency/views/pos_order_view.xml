<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- POS Order Form View - Add Currency Fields -->
    <record id="view_pos_custom_form" model="ir.ui.view">
        <field name="name">pos.order.form.addmulti</field>
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_pos_form"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="multi_currency_enable" invisible="1"/>
                <field name="primary_currency_id" invisible="1"/>
                <field name="primary_currency_symbol"/>
                <field name="foreign_currency_type"/>
                <field name="foreign_currency_amount"/>
            </field>
            <xpath expr="//page[@name='payments']/field[@name='payment_ids']/list/field[@name='amount']" position="after">
                <field name="payment_currency_type"/>
                <field name="payment_currency_amount"/>
                <field name="multi_currency_total" optional="hide"/>
                <field name="symbol_of_currency" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- POS Order List View - Add Currency Columns -->
    <record id="view_pos_order_tree_multi_currency" model="ir.ui.view">
        <field name="name">pos.order.tree.multi.currency</field>
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_order_tree"/>
        <field name="arch" type="xml">
            <field name="amount_total" position="after">
                <field name="foreign_currency_type" string="Foreign Currency Type"/>
                <field name="foreign_currency_amount" string="Amount in Foreign Currency"/>
                <field name="primary_currency_symbol" string="Primary Currency" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- POS Session Form View - Add Currency Fields -->
    <record id="view_pos_session_form_multi_currency" model="ir.ui.view">
        <field name="name">pos.session.form.multi.currency</field>
        <field name="model">pos.session</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_session_form"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="currencies_used"/>
            </field>
        </field>
    </record>

    <!-- POS Session List View - Add Currency Column -->
    <record id="view_pos_session_tree_multi_currency" model="ir.ui.view">
        <field name="name">pos.session.tree.multi.currency</field>
        <field name="model">pos.session</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_session_tree"/>
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="currencies_used" string="Currencies Used"/>
            </field>
        </field>
    </record>

    <!-- POS Payment List View - Add Currency Columns -->
    <record id="view_pos_payment_tree_multi_currency" model="ir.ui.view">
        <field name="name">pos.payment.tree.multi.currency</field>
        <field name="model">pos.payment</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_payment_tree"/>
        <field name="arch" type="xml">
            <field name="amount" position="after">
                <field name="payment_currency_type" string="Currency Type"/>
                <field name="payment_currency_amount" string="Amount in Currency"/>
                <field name="multi_currency_total" string="Foreign Amount" optional="hide"/>
                <field name="symbol_of_currency" string="Currency Symbol" optional="hide"/>
            </field>
        </field>
    </record>

</odoo>
