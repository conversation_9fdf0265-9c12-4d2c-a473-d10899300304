from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import float_is_zero


class PosConfig(models.Model):
    _inherit = "pos.config"

    pos_multi_currency_payment = fields.Boolean('Multi Currency', help='Multi Currency payment on POS')
    pos_payment_currency_ids = fields.Many2many('res.currency', string="Multi Currencies")

    def fetch_multi_currency_data(self):
        return self.pos_payment_currency_ids.read(
            ["id", "name", "symbol", "position", "rounding", "rate", "inverse_rate"]
        )


class PosOrder(models.Model):
    _inherit = "pos.order"

    multi_currency_enable = fields.Boolean(
        related="config_id.pos_multi_currency_payment", readonly=False
    )

    # Add field for the primary currency used in this order
    primary_currency_id = fields.Many2one(
        'res.currency',
        string='Primary Currency Used',
        help='The main currency used for payments in this order',
        compute='_compute_primary_currency',
        store=True
    )

    primary_currency_symbol = fields.Char(
        string='Primary Currency Symbol',
        related='primary_currency_id.symbol',
        store=True
    )

    @api.depends('payment_ids.symbol_of_currency')
    def _compute_primary_currency(self):
        """Compute the primary currency based on the highest payment amount"""
        for order in self:
            if order.payment_ids:
                # Find the payment with the highest amount
                max_payment = max(order.payment_ids, key=lambda p: p.amount, default=None)
                if max_payment and max_payment.symbol_of_currency:
                    currency = self.env['res.currency'].search([
                        ('symbol', '=', max_payment.symbol_of_currency)
                    ], limit=1)
                    order.primary_currency_id = currency.id if currency else order.currency_id.id
                else:
                    order.primary_currency_id = order.currency_id.id
            else:
                order.primary_currency_id = order.currency_id.id

    @api.model
    def _payment_fields(self, order, ui_paymentline):
        result = super(PosOrder, self)._payment_fields(order, ui_paymentline)
        if ui_paymentline.get("multi_currency_total"):
            result.update(
                {
                    "rate_of_currency": ui_paymentline.get(
                        "rate_of_currency"
                    ),
                    "symbol_of_currency": ui_paymentline.get(
                        "symbol_of_currency"
                    ),
                    "multi_currency_total": ui_paymentline.get(
                        "multi_currency_total"
                    ),
                }
            )
        else:
            result.update(
                {
                    "multi_currency_total": ui_paymentline["amount"] or 0.0,
                    "symbol_of_currency": order.pricelist_id.currency_id.symbol,
                }
            )
        return result

    def _process_payment_lines(self, pos_order, order, pos_session, draft):
        prec_acc = order.pricelist_id.currency_id.decimal_places
        if not draft and not float_is_zero(pos_order["amount_return"], prec_acc):
            cash_payment_method = pos_session.payment_method_ids.filtered(
                "is_cash_count"
            )[:1]
            if not cash_payment_method:
                raise UserError(
                    _(
                        "No cash statement found for this session. Unable to record returned cash."
                    )
                )
            return_payment_vals = {
                "name": _("return"),
                "pos_order_id": order.id,
                "amount": -pos_order["amount_return"],
                "multi_currency_total": -pos_order["amount_return"],
                "symbol_of_currency": order.pricelist_id.currency_id.symbol,
                "payment_date": fields.Datetime.now(),
                "payment_method_id": cash_payment_method.id,
                "is_change": True,
            }
            order.add_payment(return_payment_vals)


class PosSession(models.Model):
    _inherit = "pos.session"

    # Add computed field for currencies used in this session
    currencies_used = fields.Char(
        string='Currencies Used',
        compute='_compute_currencies_used',
        store=True,
        help='List of currencies used in this session'
    )

    @api.depends('order_ids.payment_ids.symbol_of_currency')
    def _compute_currencies_used(self):
        """Compute all currencies used in this session"""
        for session in self:
            currencies = set()
            for order in session.order_ids:
                for payment in order.payment_ids:
                    if payment.symbol_of_currency:
                        currencies.add(payment.symbol_of_currency)
            session.currencies_used = ', '.join(sorted(currencies)) if currencies else session.currency_id.symbol


class PosPayment(models.Model):
    _inherit = "pos.payment"

    rate_of_currency = fields.Float(
        string="Conversion Rate", help="Conversion rate of currency."
    )
    symbol_of_currency = fields.Char(string="Currency", help="Currency Symbol.")
    foreign_currency_id = fields.Many2one('res.currency', compute='_compute_foreign_currency', store=True)
    multi_currency_total = fields.Monetary(string="Amount Currency", help="Amount Total in Selected Currency.",
                                           currency_field="foreign_currency_id")
    multi_currency_enable = fields.Boolean(
        related="pos_order_id.config_id.pos_multi_currency_payment", readonly=False
    )

    @api.depends('symbol_of_currency')
    def _compute_foreign_currency(self):
        for rec in self:
            rec.foreign_currency_id = False
            currency = self.env['res.currency'].search([('symbol', 'ilike', rec.symbol_of_currency)], limit=1)
            if currency:
                rec.foreign_currency_id = currency.id

    def _export_for_ui(self, payment):
        result = super(PosPayment, self)._export_for_ui(payment)
        result["rate_of_currency"] = payment.rate_of_currency
        result["symbol_of_currency"] = payment.symbol_of_currency
        result["multi_currency_total"] = payment.multi_currency_total
        return result
