from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import float_is_zero


class PosConfig(models.Model):
    _inherit = "pos.config"

    pos_multi_currency_payment = fields.Bo<PERSON>an('Multi Currency', help='Multi Currency payment on POS')
    pos_payment_currency_ids = fields.Many2many('res.currency', string="Multi Currencies")

    def fetch_multi_currency_data(self):
        """Fetch multi-currency data with proper exchange rates"""
        try:
            currencies = self.pos_payment_currency_ids
            result = []

            for currency in currencies:
                # Calculate proper exchange rates
                company_currency = self.company_id.currency_id

                try:
                    # Get conversion rate from company currency to this currency
                    rate = company_currency._get_conversion_rate(
                        company_currency,
                        currency,
                        self.company_id,
                        fields.Date.today()
                    )

                    # Calculate inverse rate (from this currency to company currency)
                    inverse_rate = currency._get_conversion_rate(
                        currency,
                        company_currency,
                        self.company_id,
                        fields.Date.today()
                    )

                    # Log the calculated rates for debugging
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.info(f"Currency {currency.name}: rate={rate}, inverse_rate={inverse_rate}, company_currency={company_currency.name}")

                except Exception as e:
                    # Fallback to basic rates if calculation fails
                    rate = currency.rate or 1.0
                    inverse_rate = 1.0 / rate if rate != 0 else 1.0

                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning(f"Failed to calculate rates for {currency.name}, using fallback: rate={rate}, inverse_rate={inverse_rate}, error={str(e)}")

                result.append({
                    'id': currency.id,
                    'name': currency.name,
                    'symbol': currency.symbol,
                    'position': currency.position,
                    'rounding': currency.rounding,
                    'rate': rate,
                    'inverse_rate': inverse_rate,
                })

            return result
        except Exception as e:
            # Fallback to simple read if everything fails
            return self.pos_payment_currency_ids.read(
                ["id", "name", "symbol", "position", "rounding", "rate", "inverse_rate"]
            )

    def test_currency_conversion(self):
        """Test function to verify currency conversion"""
        currencies = self.pos_payment_currency_ids
        company_currency = self.company_id.currency_id

        print(f"\n=== Currency Conversion Test ===")
        print(f"Company Currency: {company_currency.name} ({company_currency.symbol})")
        print(f"Configured Currencies: {len(currencies)}")

        for currency in currencies:
            print(f"\nTesting {currency.name} ({currency.symbol}):")
            print(f"  Currency Rate: {currency.rate}")
            print(f"  Currency Inverse Rate: {getattr(currency, 'inverse_rate', 'N/A')}")

            # Test conversion
            test_amount = 100.0
            try:
                rate = company_currency._get_conversion_rate(
                    company_currency, currency, self.company_id, fields.Date.today()
                )
                converted = test_amount * rate
                print(f"  Calculated Rate: {rate}")
                print(f"  {test_amount} {company_currency.symbol} = {converted:.2f} {currency.symbol}")
            except Exception as e:
                print(f"  Error calculating rate: {str(e)}")

        print("=== End Test ===\n")

    @api.model
    def get_current_currency_rates(self, currency_ids):
        """Get current exchange rates for given currencies"""
        result = {}
        company_currency = self.env.company.currency_id

        for currency_id in currency_ids:
            currency = self.env['res.currency'].browse(currency_id)
            if currency.exists():
                # Get conversion rate from company currency to this currency
                rate = company_currency._get_conversion_rate(
                    company_currency,
                    currency,
                    self.env.company,
                    fields.Date.today()
                )

                # Calculate inverse rate (from this currency to company currency)
                inverse_rate = currency._get_conversion_rate(
                    currency,
                    company_currency,
                    self.env.company,
                    fields.Date.today()
                )

                result[currency_id] = {
                    'rate': rate,
                    'inverse_rate': inverse_rate,
                    'symbol': currency.symbol,
                    'name': currency.name,
                }

        return result


class PosOrder(models.Model):
    _inherit = "pos.order"

    multi_currency_enable = fields.Boolean(
        related="config_id.pos_multi_currency_payment", readonly=False
    )

    # Add field for the primary currency used in this order
    primary_currency_id = fields.Many2one(
        'res.currency',
        string='Primary Currency Used',
        help='The main currency used for payments in this order',
        compute='_compute_primary_currency',
        store=True
    )

    primary_currency_symbol = fields.Char(
        string='Primary Currency Symbol',
        related='primary_currency_id.symbol',
        store=True
    )

    # Add separate fields for currency type and amount
    foreign_currency_type = fields.Char(
        string='Foreign Currency Type',
        help='The type/symbol of foreign currency used in this order',
        compute='_compute_foreign_currency_details',
        store=True
    )

    foreign_currency_amount = fields.Float(
        string='Amount in Foreign Currency',
        help='Total amount in foreign currency',
        compute='_compute_foreign_currency_details',
        store=True
    )

    @api.depends('payment_ids.symbol_of_currency')
    def _compute_primary_currency(self):
        """Compute the primary currency based on the highest payment amount"""
        for order in self:
            if order.payment_ids:
                # Find the payment with the highest amount
                max_payment = max(order.payment_ids, key=lambda p: p.amount, default=None)
                if max_payment and max_payment.symbol_of_currency:
                    currency = self.env['res.currency'].search([
                        ('symbol', '=', max_payment.symbol_of_currency)
                    ], limit=1)
                    order.primary_currency_id = currency.id if currency else order.currency_id.id
                else:
                    order.primary_currency_id = order.currency_id.id
            else:
                order.primary_currency_id = order.currency_id.id

    @api.depends('payment_ids.symbol_of_currency', 'payment_ids.multi_currency_total')
    def _compute_foreign_currency_details(self):
        """Compute foreign currency type and amount"""
        for order in self:
            foreign_payments = order.payment_ids.filtered(
                lambda p: p.symbol_of_currency and p.symbol_of_currency != order.currency_id.symbol
            )

            if foreign_payments:
                # Get the most used foreign currency
                currency_totals = {}
                for payment in foreign_payments:
                    currency = payment.symbol_of_currency
                    amount = payment.multi_currency_total or 0.0
                    if currency in currency_totals:
                        currency_totals[currency] += amount
                    else:
                        currency_totals[currency] = amount

                if currency_totals:
                    # Get the currency with highest total amount
                    main_foreign_currency = max(currency_totals.keys(), key=lambda k: currency_totals[k])
                    order.foreign_currency_type = main_foreign_currency
                    order.foreign_currency_amount = currency_totals[main_foreign_currency]
                else:
                    order.foreign_currency_type = False
                    order.foreign_currency_amount = 0.0
            else:
                order.foreign_currency_type = False
                order.foreign_currency_amount = 0.0

    @api.model
    def _payment_fields(self, order, ui_paymentline):
        result = super(PosOrder, self)._payment_fields(order, ui_paymentline)
        if ui_paymentline.get("multi_currency_total"):
            result.update(
                {
                    "rate_of_currency": ui_paymentline.get(
                        "rate_of_currency"
                    ),
                    "symbol_of_currency": ui_paymentline.get(
                        "symbol_of_currency"
                    ),
                    "multi_currency_total": ui_paymentline.get(
                        "multi_currency_total"
                    ),
                }
            )
        else:
            result.update(
                {
                    "multi_currency_total": ui_paymentline["amount"] or 0.0,
                    "symbol_of_currency": order.pricelist_id.currency_id.symbol,
                }
            )
        return result

    def _process_payment_lines(self, pos_order, order, pos_session, draft):
        prec_acc = order.pricelist_id.currency_id.decimal_places
        if not draft and not float_is_zero(pos_order["amount_return"], prec_acc):
            cash_payment_method = pos_session.payment_method_ids.filtered(
                "is_cash_count"
            )[:1]
            if not cash_payment_method:
                raise UserError(
                    _(
                        "No cash statement found for this session. Unable to record returned cash."
                    )
                )
            return_payment_vals = {
                "name": _("return"),
                "pos_order_id": order.id,
                "amount": -pos_order["amount_return"],
                "multi_currency_total": -pos_order["amount_return"],
                "symbol_of_currency": order.pricelist_id.currency_id.symbol,
                "payment_date": fields.Datetime.now(),
                "payment_method_id": cash_payment_method.id,
                "is_change": True,
            }
            order.add_payment(return_payment_vals)


class PosSession(models.Model):
    _inherit = "pos.session"

    # Add computed field for currencies used in this session
    currencies_used = fields.Char(
        string='Currencies Used',
        compute='_compute_currencies_used',
        store=True,
        help='List of currencies used in this session'
    )

    @api.depends('order_ids.payment_ids.symbol_of_currency')
    def _compute_currencies_used(self):
        """Compute all currencies used in this session"""
        for session in self:
            currencies = set()
            for order in session.order_ids:
                for payment in order.payment_ids:
                    if payment.symbol_of_currency:
                        currencies.add(payment.symbol_of_currency)
            session.currencies_used = ', '.join(sorted(currencies)) if currencies else session.currency_id.symbol


class PosPayment(models.Model):
    _inherit = "pos.payment"

    rate_of_currency = fields.Float(
        string="Conversion Rate", help="Conversion rate of currency."
    )
    symbol_of_currency = fields.Char(string="Currency", help="Currency Symbol.")
    foreign_currency_id = fields.Many2one('res.currency', compute='_compute_foreign_currency', store=True)
    multi_currency_total = fields.Monetary(string="Amount Currency", help="Amount Total in Selected Currency.",
                                           currency_field="foreign_currency_id")
    multi_currency_enable = fields.Boolean(
        related="pos_order_id.config_id.pos_multi_currency_payment", readonly=False
    )

    # Add separate fields for currency type and amount display
    payment_currency_type = fields.Char(
        string='Payment Currency Type',
        help='The type/symbol of currency used for this payment',
        compute='_compute_payment_currency_details',
        store=True
    )

    payment_currency_amount = fields.Float(
        string='Payment Amount in Currency',
        help='Payment amount in the specific currency',
        compute='_compute_payment_currency_details',
        store=True
    )

    @api.depends('symbol_of_currency')
    def _compute_foreign_currency(self):
        for rec in self:
            rec.foreign_currency_id = False
            currency = self.env['res.currency'].search([('symbol', 'ilike', rec.symbol_of_currency)], limit=1)
            if currency:
                rec.foreign_currency_id = currency.id

    @api.depends('symbol_of_currency', 'multi_currency_total', 'amount')
    def _compute_payment_currency_details(self):
        """Compute payment currency type and amount"""
        for payment in self:
            if payment.symbol_of_currency:
                payment.payment_currency_type = payment.symbol_of_currency
                payment.payment_currency_amount = payment.multi_currency_total or payment.amount
            else:
                # Use local currency if no foreign currency
                payment.payment_currency_type = payment.pos_order_id.currency_id.symbol if payment.pos_order_id else ''
                payment.payment_currency_amount = payment.amount

    def _export_for_ui(self, payment):
        result = super(PosPayment, self)._export_for_ui(payment)
        result["rate_of_currency"] = payment.rate_of_currency
        result["symbol_of_currency"] = payment.symbol_of_currency
        result["multi_currency_total"] = payment.multi_currency_total
        return result
