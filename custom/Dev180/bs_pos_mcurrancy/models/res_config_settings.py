# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    pos_enable_multi_currency = fields.Boolean(
        string='Enable Multi Currency',
        related='pos_config_id.enable_multi_currency',
        readonly=False,
        help='Allow payment methods with different currencies'
    )
    
    pos_multi_currency_ids = fields.Many2many(
        'res.currency',
        related='pos_config_id.multi_currency_ids',
        readonly=False,
        string="Multi Currencies"
    )
