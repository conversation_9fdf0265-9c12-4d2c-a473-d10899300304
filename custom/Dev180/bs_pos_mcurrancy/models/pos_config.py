# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = "pos.config"

    enable_multi_currency = fields.Boolean('Enable Multi Currency', default=False)
    multi_currency_ids = fields.Many2many('res.currency', string="Multi Currencies")

    def fetch_multi_currency_data(self):
        return self.multi_currency_ids.read(
            ["id", "name", "symbol", "position", "rounding", "rate", "inverse_rate"]
        )
