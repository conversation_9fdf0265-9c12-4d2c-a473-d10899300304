# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = "pos.config"

    pos_multi_currency_payment = fields.Boolean('Multi Currency', help='Multi Currency payment on POS')
    pos_payment_currency_ids = fields.Many2many('res.currency', string="Multi Currencies")

    def fetch_multi_currency_data(self):
        return self.pos_payment_currency_ids.read(
            ["id", "name", "symbol", "position", "rounding", "rate", "inverse_rate"]
        )
