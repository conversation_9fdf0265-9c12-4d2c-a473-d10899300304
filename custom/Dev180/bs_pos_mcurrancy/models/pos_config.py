# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class PosConfig(models.Model):
    _inherit = "pos.config"

    pos_multi_currency_payment = fields.Boolean('Multi Currency', help='Multi Currency payment on POS')
    pos_payment_currency_ids = fields.Many2many('res.currency', string="Multi Currencies")

    def fetch_multi_currency_data(self):
        return self.pos_payment_currency_ids.read(
            ["id", "name", "symbol", "position", "rounding", "rate", "inverse_rate"]
        )

    @api.constrains('pricelist_id', 'use_pricelist', 'available_pricelist_ids', 'journal_id', 'invoice_journal_id', 'payment_method_ids')
    def _check_currencies(self):
        """Override currency check to allow payment methods with different currencies if multi-currency is enabled"""
        for config in self:
            if config.use_pricelist and config.pricelist_id and config.pricelist_id not in config.available_pricelist_ids:
                raise ValidationError(_("The default pricelist must be included in the available pricelists."))

            # Skip payment method currency check if multi-currency is enabled
            if not config.pos_multi_currency_payment:
                # Check if the config's payment methods are compatible with its currency
                for pm in config.payment_method_ids:
                    if pm.journal_id and pm.journal_id.currency_id and pm.journal_id.currency_id != config.currency_id:
                        raise ValidationError(_("All payment methods must be in the same currency as the Sales Journal or the company currency if that is not set."))
