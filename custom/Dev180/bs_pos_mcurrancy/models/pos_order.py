# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosOrder(models.Model):
    _inherit = "pos.order"

    def _export_for_ui(self, order):
        result = super(PosOrder, self)._export_for_ui(order)
        result['enable_multi_currency'] = order.config_id.enable_multi_currency
        return result

    def _prepare_invoice_vals(self):
        invoice_vals = super(PosOrder, self)._prepare_invoice_vals()
        return invoice_vals
