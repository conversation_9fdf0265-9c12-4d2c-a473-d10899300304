# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    # Fields following os_pos_multi_currency pattern
    rate_of_currency = fields.Float(string="Conversion Rate", help="Conversion rate of currency.")
    symbol_of_currency = fields.Char(string="Currency", help="Currency Symbol.")
    foreign_currency_id = fields.Many2one('res.currency', compute='_compute_foreign_currency', store=True)
    multi_currency_total = fields.Monetary(string="Amount Currency", help="Amount Total in Selected Currency.",
                                           currency_field="foreign_currency_id")
    selected_currency = fields.Char(string="Selected Currency", help="Selected currency name")

    @api.depends('symbol_of_currency')
    def _compute_foreign_currency(self):
        for rec in self:
            rec.foreign_currency_id = False
            if rec.symbol_of_currency:
                currency = self.env['res.currency'].search([('symbol', 'ilike', rec.symbol_of_currency)], limit=1)
                if currency:
                    rec.foreign_currency_id = currency.id

    def _export_for_ui(self, payment):
        """Export payment data for UI (following os_pos_multi_currency pattern)"""
        result = super(PosPayment, self)._export_for_ui(payment)
        result["rate_of_currency"] = payment.rate_of_currency
        result["symbol_of_currency"] = payment.symbol_of_currency
        result["multi_currency_total"] = payment.multi_currency_total
        result["selected_currency"] = payment.selected_currency
        return result

    @api.model
    def create(self, vals_list):
        """Override create to handle multi-currency payments"""
        # Temporarily disable currency validation for multi-currency payments
        return super(PosPayment, self.with_context(skip_currency_validation=True)).create(vals_list)
