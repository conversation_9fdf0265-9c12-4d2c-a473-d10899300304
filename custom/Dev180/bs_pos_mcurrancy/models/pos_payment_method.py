# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosPaymentMethod(models.Model):
    _inherit = 'pos.payment.method'

    use_specific_currency = fields.Boolean(
        string='Use Specific Currency',
        default=False,
        help='Allow this payment method to use a different currency'
    )

    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        help='Currency for this payment method'
    )

    def _export_for_ui(self, payment_method):
        """Export payment method data for UI"""
        result = super()._export_for_ui(payment_method)
        result.update({
            'use_specific_currency': payment_method.use_specific_currency,
            'currency_id': payment_method.currency_id.read(['id', 'name', 'symbol', 'position', 'rounding'])[0] if payment_method.currency_id else False,
        })
        return result
