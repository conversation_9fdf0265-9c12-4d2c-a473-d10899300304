# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosPaymentMethod(models.Model):
    _inherit = 'pos.payment.method'

    # Add currency field to payment method
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                 help='The currency used for this payment method. If empty, the company currency will be used.')
    use_specific_currency = fields.Boolean(string='Use Specific Currency', 
                                         help='If checked, this payment method will use the specified currency instead of the POS currency.')
    
    @api.onchange('use_specific_currency')
    def _onchange_use_specific_currency(self):
        """Reset currency_id when use_specific_currency is unchecked"""
        if not self.use_specific_currency:
            self.currency_id = False
    
    @api.onchange('journal_id')
    def _onchange_journal_id(self):
        """Override to set currency from journal if available"""
        res = super()._onchange_journal_id()
        if self.journal_id and self.journal_id.currency_id:
            self.currency_id = self.journal_id.currency_id
            self.use_specific_currency = True
        return res
    
    @api.model
    def _load_pos_data_fields(self, config_id):
        """Add currency fields to loaded data"""
        fields = super()._load_pos_data_fields(config_id)
        return fields + ['currency_id', 'use_specific_currency']
