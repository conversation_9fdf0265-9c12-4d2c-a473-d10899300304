<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Multi Currency fields to Payment Method form -->
    <record id="view_pos_payment_method_form_multi_currency" model="ir.ui.view">
        <field name="name">pos.payment.method.form.multi.currency</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_payment_method_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='journal_setting']" position="after">
                <group string="Multi Currency Settings" name="multi_currency">
                    <field name="use_specific_currency"/>
                    <field name="currency_id" invisible="not use_specific_currency"
                           options="{'no_create': True, 'no_open': True}"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
