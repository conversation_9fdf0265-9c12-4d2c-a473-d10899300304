<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Multi Currency settings to POS Configuration Settings -->
    <record id="res_config_settings_view_form_inherit_multi_currency" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.multi.currency</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="point_of_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='payment_methods_new']" position="after">
                <setting string="Multi-Currency Payment" help="Allow payment methods with different currencies">
                    <field name="pos_enable_multi_currency"/>
                    <div class="content-group" invisible="not pos_enable_multi_currency">
                        <div class="row mt16">
                            <label for="pos_multi_currency_ids" class="col-lg-3 o_light_label"/>
                            <field name="pos_multi_currency_ids" widget="many2many_tags"/>
                        </div>
                    </div>
                </setting>
            </xpath>
        </field>
    </record>

    <!-- Also add to individual POS Config form as backup -->
    <record id="view_pos_config_form_multi_currency" model="ir.ui.view">
        <field name="name">pos.config.form.multi.currency</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@class='oe_title']" position="after">
                <group string="Multi Currency Settings" name="multi_currency">
                    <field name="enable_multi_currency"/>
                    <field name="multi_currency_ids" widget="many2many_tags"
                           invisible="not enable_multi_currency"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
