# -*- coding: utf-8 -*-
{
    'name': 'BS POS Multi Currency',
    'version': '********.0',
    'category': 'Sales/Point of Sale',
    'summary': 'POS Multi Currency Payment - Simple Implementation',
    'description': """
        Simple POS Multi Currency Payment implementation based on os_pos_multi_currency.
        Allows payment methods with different currencies in POS.
    """,
    'author': 'BS Solutions',
    'depends': ['point_of_sale'],
    'data': [
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'bs_pos_mcurrancy/static/src/js/**/*',
            'bs_pos_mcurrancy/static/src/xml/**/*',
        ],
        # 'web.assets_qweb': [
        # ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
