/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { PosPayment } from "@point_of_sale/app/models/pos_payment";

patch(PosPayment.prototype, {
    setup(obj, options) {
        super.setup(...arguments);
        this.selected_currency = this.selected_currency || false;
        this.rate_of_currency = this.rate_of_currency || 0.0;
        this.symbol_of_currency = this.symbol_of_currency || '';
        this.multi_currency_total = this.multi_currency_total || 0.0;
    },

    get_selected_currency() {
        return this.selected_currency;
    },

    get_currency_symbol() {
        return this.symbol_of_currency;
    },

    get_currency_rate() {
        return this.rate_of_currency;
    },

    get_converted_amount() {
        const convertedAmount = (this.rate_of_currency * this.amount).toFixed(2);
        return convertedAmount || 0;
    },

    get_currency_amount_paid() {
        return Number(this.multi_currency_total);
    },

    init_from_JSON(json) {
        super.init_from_JSON(...arguments);
        this.selected_currency = json.selected_currency;
        this.symbol_of_currency = json.symbol_of_currency;
        this.rate_of_currency = json.rate_of_currency;
        this.multi_currency_total = json.multi_currency_total;
    },

    export_as_JSON() {
        let json = super.export_as_JSON(...arguments);
        json.selected_currency = this.get_selected_currency();
        json.symbol_of_currency = this.get_currency_symbol();
        json.rate_of_currency = this.get_currency_rate() || 0.0;
        json.multi_currency_total = this.get_currency_amount_paid() || 0.0;
        return json;
    },

    export_for_printing() {
        var receipt = super.export_for_printing(...arguments);
        receipt.selected_currency = this.get_selected_currency();
        receipt.symbol_of_currency = this.get_currency_symbol();
        receipt.rate_of_currency = this.get_currency_rate() || 0.0;
        var rounded_rate = receipt.rate_of_currency;
        receipt.rounded_currency_rate = Math.round(rounded_rate * 100) / 100;
        receipt.multi_currency_total = this.get_currency_amount_paid() || 0.0;
        return receipt;
    }
});
