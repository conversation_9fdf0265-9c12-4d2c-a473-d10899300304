#!/usr/bin/env python3
import psycopg2
import sys

def add_columns():
    try:
        # Connect to database
        conn = psycopg2.connect(
            host='localhost',
            port=5433,
            database='1002',
            user='odoo'
        )
        cur = conn.cursor()

        # Check if columns exist and add them if they don't
        columns_to_add = [
            ('multi_currency_total', 'NUMERIC DEFAULT 0.0'),
            ('rate_of_currency', 'DOUBLE PRECISION DEFAULT 0.0'),
            ('selected_currency', 'VARCHAR'),
            ('symbol_of_currency', 'VARCHAR'),
            ('foreign_currency_id', 'INTEGER'),
        ]

        for column_name, column_def in columns_to_add:
            # Check if column exists
            cur.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name='pos_payment' AND column_name=%s
            """, (column_name,))

            if not cur.fetchone():
                # Add the column
                cur.execute(f"ALTER TABLE pos_payment ADD COLUMN {column_name} {column_def}")
                print(f"Added column {column_name} to pos_payment table")
            else:
                print(f"Column {column_name} already exists in pos_payment table")

        # Add foreign key constraint for foreign_currency_id if it doesn't exist
        cur.execute("""
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name='pos_payment' AND constraint_name='pos_payment_foreign_currency_id_fkey'
        """)

        if not cur.fetchone():
            try:
                cur.execute("""
                    ALTER TABLE pos_payment
                    ADD CONSTRAINT pos_payment_foreign_currency_id_fkey
                    FOREIGN KEY (foreign_currency_id) REFERENCES res_currency(id) ON DELETE SET NULL
                """)
                print("Added foreign key constraint for foreign_currency_id")
            except Exception as e:
                print(f"Could not add foreign key constraint: {e}")

        # Commit changes
        conn.commit()
        cur.close()
        conn.close()
        print("Database update completed successfully!")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    add_columns()
